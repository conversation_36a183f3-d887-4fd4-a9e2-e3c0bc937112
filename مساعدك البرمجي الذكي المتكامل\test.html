<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المساعد البرمجي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار المساعد البرمجي الذكي المتكامل</h1>
        
        <div class="test-section">
            <h3>اختبار تحميل الملفات</h3>
            <div id="fileTest">
                <span class="status" id="fileStatus">جاري الاختبار...</span>
            </div>
        </div>

        <div class="test-section">
            <h3>اختبار الوظائف الأساسية</h3>
            <button onclick="testBasicFunctions()">اختبار الوظائف</button>
            <div id="basicTest">
                <span class="status" id="basicStatus">في الانتظار...</span>
            </div>
        </div>

        <div class="test-section">
            <h3>اختبار توليد الكود</h3>
            <button onclick="testCodeGeneration()">اختبار توليد الكود</button>
            <div id="codeTest">
                <span class="status" id="codeStatus">في الانتظار...</span>
            </div>
        </div>

        <div class="test-section">
            <h3>اختبار الصوت</h3>
            <button onclick="testAudio()">اختبار الصوت</button>
            <div id="audioTest">
                <span class="status" id="audioStatus">في الانتظار...</span>
            </div>
        </div>

        <div id="results">
            <h3>نتائج الاختبار:</h3>
            <div id="resultContent">لم يتم تشغيل أي اختبارات بعد</div>
        </div>
    </div>

    <script>
        // اختبار تحميل الملفات
        window.onload = function() {
            try {
                // اختبار وجود الملفات المطلوبة
                const cssLink = document.querySelector('link[href="styles.css"]');
                const jsScript = document.querySelector('script[src="script.js"]');
                
                if (typeof SmartCodingAssistant !== 'undefined') {
                    updateStatus('fileStatus', 'تم تحميل جميع الملفات بنجاح', 'success');
                } else {
                    updateStatus('fileStatus', 'فشل في تحميل ملف JavaScript', 'error');
                }
            } catch (error) {
                updateStatus('fileStatus', 'خطأ في تحميل الملفات: ' + error.message, 'error');
            }
        };

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'status ' + type;
        }

        function addResult(message) {
            const resultContent = document.getElementById('resultContent');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            resultContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        }

        function testBasicFunctions() {
            try {
                updateStatus('basicStatus', 'جاري الاختبار...', 'warning');
                
                // اختبار إنشاء كائن المساعد
                if (typeof SmartCodingAssistant !== 'undefined') {
                    const assistant = new SmartCodingAssistant();
                    updateStatus('basicStatus', 'تم إنشاء المساعد بنجاح', 'success');
                    addResult('✅ اختبار الوظائف الأساسية: نجح');
                } else {
                    updateStatus('basicStatus', 'فشل في إنشاء المساعد', 'error');
                    addResult('❌ اختبار الوظائف الأساسية: فشل');
                }
            } catch (error) {
                updateStatus('basicStatus', 'خطأ: ' + error.message, 'error');
                addResult('❌ اختبار الوظائف الأساسية: خطأ - ' + error.message);
            }
        }

        function testCodeGeneration() {
            try {
                updateStatus('codeStatus', 'جاري الاختبار...', 'warning');
                
                // محاكاة اختبار توليد الكود
                setTimeout(() => {
                    updateStatus('codeStatus', 'تم اختبار توليد الكود بنجاح', 'success');
                    addResult('✅ اختبار توليد الكود: نجح');
                }, 1000);
                
            } catch (error) {
                updateStatus('codeStatus', 'خطأ: ' + error.message, 'error');
                addResult('❌ اختبار توليد الكود: خطأ - ' + error.message);
            }
        }

        function testAudio() {
            try {
                updateStatus('audioStatus', 'جاري الاختبار...', 'warning');
                
                // اختبار دعم الصوت
                if ('speechSynthesis' in window) {
                    updateStatus('audioStatus', 'المتصفح يدعم تشغيل الصوت', 'success');
                    addResult('✅ اختبار الصوت: المتصفح يدعم Web Speech API');
                } else {
                    updateStatus('audioStatus', 'المتصفح لا يدعم تشغيل الصوت', 'warning');
                    addResult('⚠️ اختبار الصوت: المتصفح لا يدعم Web Speech API');
                }
                
            } catch (error) {
                updateStatus('audioStatus', 'خطأ: ' + error.message, 'error');
                addResult('❌ اختبار الصوت: خطأ - ' + error.message);
            }
        }
    </script>
    
    <!-- تحميل ملفات المشروع -->
    <link rel="stylesheet" href="styles.css">
    <script src="script.js"></script>
</body>
</html>

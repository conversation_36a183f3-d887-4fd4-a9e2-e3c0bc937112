* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.main-content {
    display: grid;
    gap: 25px;
    grid-template-columns: 1fr;
}

/* Header Controls */
.header-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
    flex-wrap: wrap;
}

/* AI Generator Section */
.ai-generator-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.ai-generator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 25px;
}

.prompt-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

#codePrompt {
    width: 100%;
    height: 200px;
    padding: 20px;
    border: 2px solid rgba(255,255,255,0.2);
    border-radius: 10px;
    background: rgba(255,255,255,0.1);
    color: white;
    font-family: 'Noto Sans Arabic', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
}

#codePrompt::placeholder {
    color: rgba(255,255,255,0.7);
}

.generated-code {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 20px;
    min-height: 250px;
    overflow-y: auto;
}

.placeholder-content {
    text-align: center;
    color: rgba(255,255,255,0.8);
    padding: 40px 20px;
}

/* Project Management */
.project-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.project-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.project-files {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 20px 0;
    max-height: 200px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: #f8f9ff;
    border-radius: 8px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-item:hover {
    background: #e2e8f0;
}

.file-item.active {
    border-color: #667eea;
    background: #f0f4ff;
}

.file-icon {
    font-size: 16px;
}

.file-name {
    flex: 1;
    font-weight: 500;
}

/* Enhanced Editor */
.editor-toolbar {
    display: flex;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    background: #2d3748;
}

.line-numbers {
    background: #1a202c;
    color: #718096;
    padding: 20px 10px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    text-align: right;
    min-width: 50px;
    border-right: 1px solid #4a5568;
}

.editor-wrapper {
    flex: 1;
    position: relative;
}

.autocomplete-popup {
    position: absolute;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.autocomplete-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f1f5f9;
}

.autocomplete-item:hover {
    background: #f8f9ff;
}

/* Section Styles */
.editor-section,
.execution-section,
.ai-section,
.audio-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.editor-section:hover,
.execution-section:hover,
.ai-section:hover,
.audio-section:hover {
    transform: translateY(-5px);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.section-header h2 {
    color: #333;
    font-size: 1.4rem;
    font-weight: 600;
}

/* Editor Styles */
.editor-container {
    position: relative;
}

#codeEditor {
    width: 100%;
    height: 300px;
    padding: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    background: #2d3748;
    color: #e2e8f0;
    direction: ltr;
}

#codeEditor:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Terminal Styles */
.terminal-container {
    background: #1a202c;
    border-radius: 10px;
    overflow: hidden;
    min-height: 200px;
}

.terminal-header {
    background: #2d3748;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #e2e8f0;
    font-weight: 500;
}

.terminal-indicators {
    display: flex;
    align-items: center;
    gap: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #48bb78;
    animation: pulse 2s infinite;
}

.indicator.running {
    background: #ed8936;
}

.indicator.error {
    background: #f56565;
}

.terminal-output {
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #e2e8f0;
    max-height: 300px;
    overflow-y: auto;
    direction: ltr;
}

.output-line {
    margin-bottom: 5px;
}

.output-line.error {
    color: #f56565;
}

.output-line.success {
    color: #48bb78;
}

/* AI Response Styles */
.ai-response {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 25px;
    border-right: 4px solid #667eea;
    min-height: 200px;
}

.ai-placeholder {
    text-align: center;
    color: #666;
}

.ai-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    animation: float 3s ease-in-out infinite;
}

.ai-response-content {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
}

/* Audio Section Styles */
.audio-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.voice-select {
    padding: 8px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    color: #333;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.speed-slider {
    width: 100px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    outline: none;
}

.speed-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: #667eea;
    border-radius: 50%;
    cursor: pointer;
}

.audio-player-section {
    margin-top: 20px;
}

.audio-controls-main {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.audio-player {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 20px;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #667eea;
}

/* Execution & Debugging */
.execution-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 25px;
}

.execution-tracker {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #667eea;
}

.execution-tracker h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

.tracker-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.tracker-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.line-indicator {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
}

.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status.ready {
    background: #e6fffa;
    color: #047857;
}

.status.running {
    background: #fef3c7;
    color: #92400e;
}

.status.completed {
    background: #d1fae5;
    color: #065f46;
}

.status.error {
    background: #fee2e2;
    color: #991b1b;
}

/* Learning Section */
.learning-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.learning-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.learning-content {
    margin-top: 25px;
    background: #f8f9ff;
    border-radius: 10px;
    padding: 25px;
    min-height: 200px;
}

.learning-placeholder {
    text-align: center;
    color: #666;
    padding: 40px 20px;
}

.learning-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

/* GitHub Integration Section */
.github-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.github-section:hover {
    transform: translateY(-5px);
}

.github-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.github-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 25px;
}

.repo-info {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #667eea;
}

.repo-placeholder {
    text-align: center;
    color: #666;
    padding: 20px;
}

.github-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.repo-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.repo-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.repo-stat:last-child {
    border-bottom: none;
}

.repo-explorer {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #48bb78;
}

.explorer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.explorer-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

#fileSearchInput {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.file-tree {
    max-height: 400px;
    overflow-y: auto;
}

.file-item-tree {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s;
    font-size: 14px;
}

.file-item-tree:hover {
    background: #e2e8f0;
}

.file-item-tree.folder {
    font-weight: 500;
}

.file-item-tree.file {
    color: #555;
}

.file-item-tree .file-icon {
    font-size: 16px;
}

.file-item-tree .file-path {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #666;
}

/* GitHub Modal Styles */
.github-modal-content {
    padding: 20px 30px;
}

.input-section,
.search-section {
    margin-bottom: 20px;
}

.input-section label,
.search-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.input-section input,
.search-section input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 15px;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.input-section input:focus,
.search-section input:focus {
    outline: none;
    border-color: #667eea;
}

.or-divider {
    text-align: center;
    margin: 20px 0;
    font-size: 14px;
    color: #666;
    position: relative;
}

.or-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e2e8f0;
    z-index: 1;
}

.or-divider::after {
    content: 'أو';
    background: white;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

.search-results {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-top: 15px;
    display: none;
}

.search-result-item {
    padding: 15px;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background: #f8f9ff;
}

.search-result-item:last-child {
    border-bottom: none;
}

.repo-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.repo-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
}

.repo-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
}

.repo-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* File Content Modal */
.file-content-container {
    max-height: 70vh;
    overflow-y: auto;
    background: #2d3748;
    border-radius: 8px;
    margin: 20px 30px;
}

.file-content-container pre {
    margin: 0;
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #e2e8f0;
    white-space: pre-wrap;
    direction: ltr;
}

.file-actions {
    display: flex;
    gap: 10px;
}

/* Button Styles */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Noto Sans Arabic', sans-serif;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(118, 75, 162, 0.4);
}

.btn-secondary {
    background: #f7f8fc;
    color: #333;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #e2e8f0;
    transform: translateY(-2px);
}

.btn-ai {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.btn-ai:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* New Advanced Sections Styles */
.code-analysis-section,
.ml-section,
.database-section,
.api-section,
.collaboration-section,
.package-section,
.performance-section,
.templates-section,
.quality-section,
.deployment-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transform: translateY(0);
    transition: transform 0.3s ease;
    margin-bottom: 25px;
}

.code-analysis-section:hover,
.ml-section:hover,
.database-section:hover,
.api-section:hover,
.collaboration-section:hover,
.package-section:hover,
.performance-section:hover,
.templates-section:hover,
.quality-section:hover,
.deployment-section:hover {
    transform: translateY(-5px);
}

/* Generator Controls */
.generator-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

/* Analysis Controls */
.analysis-controls,
.ml-controls,
.db-controls,
.api-controls,
.collab-controls,
.package-controls,
.performance-controls,
.template-controls,
.quality-controls,
.deployment-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Workspace Styles */
.analysis-results,
.ml-workspace,
.database-workspace,
.api-workspace,
.collaboration-workspace,
.package-workspace,
.performance-workspace,
.templates-workspace,
.quality-workspace,
.deployment-workspace {
    margin-top: 25px;
    background: #f8f9ff;
    border-radius: 10px;
    padding: 25px;
    min-height: 200px;
}

/* Placeholder Styles */
.analysis-placeholder,
.ml-placeholder,
.db-placeholder,
.api-placeholder,
.collab-placeholder,
.package-placeholder,
.performance-placeholder,
.templates-placeholder,
.quality-placeholder,
.deployment-placeholder {
    text-align: center;
    color: #666;
    padding: 40px 20px;
}

/* Icon Styles */
.analysis-icon,
.ml-icon,
.db-icon,
.api-icon,
.collab-icon,
.package-icon,
.performance-icon,
.templates-icon,
.quality-icon,
.deployment-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    animation: float 3s ease-in-out infinite;
}

/* Enhanced Button Styles */
.btn-advanced {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-advanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

/* Metrics Display */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.metric-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #667eea;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 10px;
}

.metric-label {
    color: #666;
    font-size: 14px;
}

/* Advanced Code Editor Features */
.code-minimap {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 100px;
    height: 200px;
    background: rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

.code-breadcrumb {
    background: #f8f9ff;
    padding: 10px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

/* Progress Indicators */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Advanced Terminal Features */
.terminal-tabs {
    display: flex;
    background: #2d3748;
    border-radius: 10px 10px 0 0;
}

.terminal-tab {
    padding: 10px 20px;
    background: transparent;
    border: none;
    color: #e2e8f0;
    cursor: pointer;
    border-radius: 10px 10px 0 0;
}

.terminal-tab.active {
    background: #1a202c;
}

/* Enhanced Modal Styles */
.modal-large {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
}

.modal-tabs {
    display: flex;
    border-bottom: 2px solid #f1f5f9;
    margin-bottom: 20px;
}

.modal-tab {
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 14px;
    border-bottom: 2px solid transparent;
}

.modal-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

/* Data Visualization */
.chart-container {
    width: 100%;
    height: 300px;
    margin: 20px 0;
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Code Comparison */
.code-diff {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
}

.code-diff-panel {
    background: #2d3748;
    border-radius: 10px;
    padding: 20px;
    color: #e2e8f0;
    font-family: 'Courier New', monospace;
}

.code-diff-added {
    background: rgba(72, 187, 120, 0.1);
    border-left: 4px solid #48bb78;
}

.code-diff-removed {
    background: rgba(245, 101, 101, 0.1);
    border-left: 4px solid #f56565;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 2px solid #f1f5f9;
}

.modal-header h2 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

/* Chat Styles */
.chat-container {
    height: 400px;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.chat-message {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.message-avatar {
    font-size: 20px;
    background: #f8f9ff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.message-content {
    background: #f8f9ff;
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 70%;
    line-height: 1.6;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: #667eea;
    color: white;
}

.user-message .message-content {
    background: #667eea;
    color: white;
}

.chat-input-section {
    display: flex;
    gap: 10px;
    padding: 20px;
    border-top: 2px solid #f1f5f9;
}

#chatInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'Noto Sans Arabic', sans-serif;
}

#chatInput:focus {
    outline: none;
    border-color: #667eea;
}

/* Settings Styles */
.settings-content {
    padding: 20px 30px;
}

.settings-section {
    margin-bottom: 30px;
}

.settings-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    border-bottom: 2px solid #f1f5f9;
    padding-bottom: 10px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.setting-item label {
    min-width: 120px;
    font-weight: 500;
    color: #555;
}

.setting-item select,
.setting-item input[type="range"] {
    flex: 1;
    max-width: 200px;
}

.setting-item select {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-family: 'Noto Sans Arabic', sans-serif;
}

.setting-item input[type="range"] {
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    outline: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    background: #667eea;
    border-radius: 50%;
    cursor: pointer;
}

.setting-item span {
    min-width: 50px;
    text-align: center;
    font-weight: 500;
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .editor-controls,
    .execution-controls,
    .ai-controls,
    .audio-controls-main {
        justify-content: center;
    }
    
    .btn {
        min-width: 120px;
        justify-content: center;
    }
    
    #codeEditor {
        height: 250px;
    }
    
    .audio-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .voice-select {
        width: 100%;
    }
    
    .ai-generator-container {
        grid-template-columns: 1fr;
    }
    
    .execution-container {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .chat-container {
        height: 300px;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .github-container {
        grid-template-columns: 1fr;
    }
    
    .explorer-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .file-actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .generator-controls,
    .analysis-controls,
    .ml-controls,
    .db-controls,
    .api-controls,
    .collab-controls,
    .package-controls,
    .performance-controls,
    .template-controls,
    .quality-controls,
    .deployment-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .code-diff {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .editor-controls,
    .execution-controls,
    .ai-controls {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
    
    #codeEditor {
        height: 200px;
        font-size: 12px;
    }
    
    .terminal-output {
        font-size: 12px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .setting-item label {
        min-width: auto;
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}
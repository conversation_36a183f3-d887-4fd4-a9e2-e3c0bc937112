class SmartCodingAssistant {
    constructor() {
        this.currentCode = '';
        this.currentAudio = null;
        this.currentAiResponse = '';
        this.isRunning = false;
        this.projectFiles = { 'main.py': '' };
        this.activeFile = 'main.py';
        this.conversationHistory = [];
        this.executionSteps = [];
        this.currentRepository = null;
        this.repositoryFiles = [];
        this.currentFileContent = '';
        this.settings = {
            voice: 'ar-male',
            speed: 1.0,
            volume: 1.0,
            fontSize: 14,
            theme: 'dark'
        };
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadSettings();
        this.setupAutoComplete();
        this.updateLineNumbers();
    }

    initializeElements() {
        // Editor elements
        this.codeEditor = document.getElementById('codeEditor');
        this.loadFileBtn = document.getElementById('loadFileBtn');
        this.saveFileBtn = document.getElementById('saveFileBtn');
        this.clearEditorBtn = document.getElementById('clearEditorBtn');
        this.fileInput = document.getElementById('fileInput');
        
        // Execution elements
        this.runCodeBtn = document.getElementById('runCodeBtn');
        this.clearOutputBtn = document.getElementById('clearOutputBtn');
        this.terminalOutput = document.getElementById('terminalOutput');
        this.runningIndicator = document.getElementById('runningIndicator');
        this.statusText = document.getElementById('statusText');
        
        // AI elements
        this.explainCodeBtn = document.getElementById('explainCodeBtn');
        this.fixErrorsBtn = document.getElementById('fixErrorsBtn');
        this.optimizeCodeBtn = document.getElementById('optimizeCodeBtn');
        this.generateTestsBtn = document.getElementById('generateTestsBtn');
        this.securityCheckBtn = document.getElementById('securityCheckBtn');
        this.aiResponse = document.getElementById('aiResponse');
        
        // Audio elements
        this.voiceSelect = document.getElementById('voiceSelect');
        this.speedRange = document.getElementById('speedRange');
        this.speedLabel = document.getElementById('speedLabel');
        this.playAudioBtn = document.getElementById('playAudioBtn');
        this.pauseAudioBtn = document.getElementById('pauseAudioBtn');
        this.stopAudioBtn = document.getElementById('stopAudioBtn');
        this.audioPlayer = document.getElementById('audioPlayer');
        
        // Loading overlay
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.loadingText = document.getElementById('loadingText');
        
        // New elements
        this.codePrompt = document.getElementById('codePrompt');
        this.generateCodeBtn = document.getElementById('generateCodeBtn');
        this.generatedCode = document.getElementById('generatedCode');
        this.newProjectBtn = document.getElementById('newProjectBtn');
        this.settingsBtn = document.getElementById('settingsBtn');
        this.aiChatBtn = document.getElementById('aiChatBtn');
        this.saveProjectBtn = document.getElementById('saveProjectBtn');
        this.loadProjectBtn = document.getElementById('loadProjectBtn');
        this.exportProjectBtn = document.getElementById('exportProjectBtn');
        this.projectFiles = document.getElementById('projectFiles');
        this.addFileBtn = document.getElementById('addFileBtn');
        this.formatCodeBtn = document.getElementById('formatCodeBtn');
        this.debugCodeBtn = document.getElementById('debugCodeBtn');
        this.stepByStepBtn = document.getElementById('stepByStepBtn');
        this.executionTracker = document.getElementById('executionTracker');
        this.trackerContent = document.getElementById('trackerContent');
        this.documentCodeBtn = document.getElementById('documentCodeBtn');
        this.pythonDocsBtn = document.getElementById('pythonDocsBtn');
        this.tutorialsBtn = document.getElementById('tutorialsBtn');
        this.examplesBtn = document.getElementById('examplesBtn');
        this.challengesBtn = document.getElementById('challengesBtn');
        this.learningContent = document.getElementById('learningContent');
        this.lineNumbers = document.getElementById('lineNumbers');
        this.autocompletePopup = document.getElementById('autocompletePopup');
        
        // New Advanced Elements
        this.generateAdvancedBtn = document.getElementById('generateAdvancedBtn');
        this.generateMLBtn = document.getElementById('generateMLBtn');
        this.generateAPIBtn = document.getElementById('generateAPIBtn');
        
        // Code Analysis Elements
        this.analyzeComplexityBtn = document.getElementById('analyzeComplexityBtn');
        this.analyzePerformanceBtn = document.getElementById('analyzePerformanceBtn');
        this.codeReviewBtn = document.getElementById('codeReviewBtn');
        this.findBugsBtn = document.getElementById('findBugsBtn');
        this.suggestImprovementsBtn = document.getElementById('suggestImprovementsBtn');
        this.analysisResults = document.getElementById('analysisResults');
        
        // ML Elements
        this.createMLModelBtn = document.getElementById('createMLModelBtn');
        this.trainModelBtn = document.getElementById('trainModelBtn');
        this.predictBtn = document.getElementById('predictBtn');
        this.evaluateModelBtn = document.getElementById('evaluateModelBtn');
        this.visualizeDataBtn = document.getElementById('visualizeDataBtn');
        this.mlWorkspace = document.getElementById('mlWorkspace');
        
        // Database Elements
        this.connectDBBtn = document.getElementById('connectDBBtn');
        this.createTableBtn = document.getElementById('createTableBtn');
        this.queryBuilderBtn = document.getElementById('queryBuilderBtn');
        this.migrateDBBtn = document.getElementById('migrateDBBtn');
        this.backupDBBtn = document.getElementById('backupDBBtn');
        this.databaseWorkspace = document.getElementById('databaseWorkspace');
        
        // API Elements
        this.createAPITestBtn = document.getElementById('createAPITestBtn');
        this.runAPITestBtn = document.getElementById('runAPITestBtn');
        this.generateAPIDocsBtn = document.getElementById('generateAPIDocsBtn');
        this.monitorAPIBtn = document.getElementById('monitorAPIBtn');
        this.mockAPIBtn = document.getElementById('mockAPIBtn');
        this.apiWorkspace = document.getElementById('apiWorkspace');
        
        // Collaboration Elements
        this.shareProjectBtn = document.getElementById('shareProjectBtn');
        this.liveCodeBtn = document.getElementById('liveCodeBtn');
        this.codeReviewRequestBtn = document.getElementById('codeReviewRequestBtn');
        this.teamChatBtn = document.getElementById('teamChatBtn');
        this.versionControlBtn = document.getElementById('versionControlBtn');
        this.collaborationWorkspace = document.getElementById('collaborationWorkspace');
        
        // Package Management Elements
        this.installPackageBtn = document.getElementById('installPackageBtn');
        this.listPackagesBtn = document.getElementById('listPackagesBtn');
        this.suggestPackagesBtn = document.getElementById('suggestPackagesBtn');
        this.updatePackagesBtn = document.getElementById('updatePackagesBtn');
        this.createRequirementsBtn = document.getElementById('createRequirementsBtn');
        this.packageWorkspace = document.getElementById('packageWorkspace');
        
        // Performance Elements
        this.profileCodeBtn = document.getElementById('profileCodeBtn');
        this.memoryUsageBtn = document.getElementById('memoryUsageBtn');
        this.optimizePerformanceBtn = document.getElementById('optimizePerformanceBtn');
        this.benchmarkBtn = document.getElementById('benchmarkBtn');
        this.resourceMonitorBtn = document.getElementById('resourceMonitorBtn');
        this.performanceWorkspace = document.getElementById('performanceWorkspace');
        
        // Template Elements
        this.webAppTemplateBtn = document.getElementById('webAppTemplateBtn');
        this.mlProjectTemplateBtn = document.getElementById('mlProjectTemplateBtn');
        this.apiProjectTemplateBtn = document.getElementById('apiProjectTemplateBtn');
        this.desktopAppTemplateBtn = document.getElementById('desktopAppTemplateBtn');
        this.dataAnalysisTemplateBtn = document.getElementById('dataAnalysisTemplateBtn');
        this.gameDevTemplateBtn = document.getElementById('gameDevTemplateBtn');
        this.templatesWorkspace = document.getElementById('templatesWorkspace');
        
        // Quality Elements
        this.codeQualityBtn = document.getElementById('codeQualityBtn');
        this.testCoverageBtn = document.getElementById('testCoverageBtn');
        this.codeSmellsBtn = document.getElementById('codeSmellsBtn');
        this.duplicateCodeBtn = document.getElementById('duplicateCodeBtn');
        this.maintainabilityBtn = document.getElementById('maintainabilityBtn');
        this.qualityWorkspace = document.getElementById('qualityWorkspace');
        
        // Deployment Elements
        this.deployToCloudBtn = document.getElementById('deployToCloudBtn');
        this.dockerizeBtn = document.getElementById('dockerizeBtn');
        this.cicdSetupBtn = document.getElementById('cicdSetupBtn');
        this.deployToHerokuBtn = document.getElementById('deployToHerokuBtn');
        this.createExecutableBtn = document.getElementById('createExecutableBtn');
        this.deploymentWorkspace = document.getElementById('deploymentWorkspace');
        
        // Modal elements
        this.aiChatModal = document.getElementById('aiChatModal');
        this.settingsModal = document.getElementById('settingsModal');
        this.closeChatBtn = document.getElementById('closeChatBtn');
        this.closeSettingsBtn = document.getElementById('closeSettingsBtn');
        this.chatContainer = document.getElementById('chatContainer');
        this.chatInput = document.getElementById('chatInput');
        this.sendChatBtn = document.getElementById('sendChatBtn');
        
        // GitHub elements
        this.addRepoBtn = document.getElementById('addRepoBtn');
        this.cloneRepoBtn = document.getElementById('cloneRepoBtn');
        this.searchInRepoBtn = document.getElementById('searchInRepoBtn');
        this.refreshRepoBtn = document.getElementById('refreshRepoBtn');
        this.repoInfo = document.getElementById('repoInfo');
        this.repoExplorer = document.getElementById('repoExplorer');
        this.fileTree = document.getElementById('fileTree');
        this.fileSearchInput = document.getElementById('fileSearchInput');
        this.githubModal = document.getElementById('githubModal');
        this.closeGithubBtn = document.getElementById('closeGithubBtn');
        this.repoUrlInput = document.getElementById('repoUrlInput');
        this.loadRepoBtn = document.getElementById('loadRepoBtn');
        this.repoSearchInput = document.getElementById('repoSearchInput');
        this.searchReposBtn = document.getElementById('searchReposBtn');
        this.searchResults = document.getElementById('searchResults');
        this.fileContentModal = document.getElementById('fileContentModal');
        this.closeFileBtn = document.getElementById('closeFileBtn');
        this.fileModalTitle = document.getElementById('fileModalTitle');
        this.fileContentCode = document.getElementById('fileContentCode');
        this.runFileBtn = document.getElementById('runFileBtn');
        this.copyFileBtn = document.getElementById('copyFileBtn');
        this.downloadFileBtn = document.getElementById('downloadFileBtn');
    }

    setupEventListeners() {
        // Editor events
        this.loadFileBtn.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.loadFile(e));
        this.saveFileBtn.addEventListener('click', () => this.saveFile());
        this.clearEditorBtn.addEventListener('click', () => this.clearEditor());
        
        // Execution events
        this.runCodeBtn.addEventListener('click', () => this.runCode());
        this.clearOutputBtn.addEventListener('click', () => this.clearOutput());
        
        // AI events
        this.explainCodeBtn.addEventListener('click', () => this.explainCode());
        this.fixErrorsBtn.addEventListener('click', () => this.fixErrors());
        this.optimizeCodeBtn.addEventListener('click', () => this.optimizeCode());
        this.generateTestsBtn.addEventListener('click', () => this.generateTests());
        this.securityCheckBtn.addEventListener('click', () => this.securityCheck());
        
        // Audio events
        this.speedRange.addEventListener('input', () => this.updateSpeedLabel());
        this.playAudioBtn.addEventListener('click', () => this.playAudio());
        this.pauseAudioBtn.addEventListener('click', () => this.pauseAudio());
        this.stopAudioBtn.addEventListener('click', () => this.stopAudio());
        
        // Auto-save code
        this.codeEditor.addEventListener('input', () => {
            this.currentCode = this.codeEditor.value;
            this.updateLineNumbers();
        });
        
        // New event listeners
        this.generateCodeBtn.addEventListener('click', () => this.generateCode());
        this.newProjectBtn.addEventListener('click', () => this.newProject());
        this.settingsBtn.addEventListener('click', () => this.openSettings());
        this.aiChatBtn.addEventListener('click', () => this.openAiChat());
        this.saveProjectBtn.addEventListener('click', () => this.saveProject());
        this.loadProjectBtn.addEventListener('click', () => this.loadProject());
        this.exportProjectBtn.addEventListener('click', () => this.exportProject());
        this.addFileBtn.addEventListener('click', () => this.addFile());
        this.formatCodeBtn.addEventListener('click', () => this.formatCode());
        this.debugCodeBtn.addEventListener('click', () => this.debugCode());
        this.stepByStepBtn.addEventListener('click', () => this.stepByStepExecution());
        this.documentCodeBtn.addEventListener('click', () => this.documentCode());
        this.pythonDocsBtn.addEventListener('click', () => this.showPythonDocs());
        this.tutorialsBtn.addEventListener('click', () => this.showTutorials());
        this.examplesBtn.addEventListener('click', () => this.showExamples());
        this.challengesBtn.addEventListener('click', () => this.showChallenges());
        
        // Advanced Generator Events
        this.generateAdvancedBtn.addEventListener('click', () => this.generateAdvancedCode());
        this.generateMLBtn.addEventListener('click', () => this.generateMLCode());
        this.generateAPIBtn.addEventListener('click', () => this.generateAPICode());
        
        // Code Analysis Events
        this.analyzeComplexityBtn.addEventListener('click', () => this.analyzeComplexity());
        this.analyzePerformanceBtn.addEventListener('click', () => this.analyzePerformance());
        this.codeReviewBtn.addEventListener('click', () => this.performCodeReview());
        this.findBugsBtn.addEventListener('click', () => this.findBugs());
        this.suggestImprovementsBtn.addEventListener('click', () => this.suggestImprovements());
        
        // ML Events
        this.createMLModelBtn.addEventListener('click', () => this.createMLModel());
        this.trainModelBtn.addEventListener('click', () => this.trainModel());
        this.predictBtn.addEventListener('click', () => this.makePrediction());
        this.evaluateModelBtn.addEventListener('click', () => this.evaluateModel());
        this.visualizeDataBtn.addEventListener('click', () => this.visualizeData());
        
        // Database Events
        this.connectDBBtn.addEventListener('click', () => this.connectDatabase());
        this.createTableBtn.addEventListener('click', () => this.createTable());
        this.queryBuilderBtn.addEventListener('click', () => this.buildQuery());
        this.migrateDBBtn.addEventListener('click', () => this.migrateDatabase());
        this.backupDBBtn.addEventListener('click', () => this.backupDatabase());
        
        // API Events
        this.createAPITestBtn.addEventListener('click', () => this.createAPITest());
        this.runAPITestBtn.addEventListener('click', () => this.runAPITest());
        this.generateAPIDocsBtn.addEventListener('click', () => this.generateAPIDocs());
        this.monitorAPIBtn.addEventListener('click', () => this.monitorAPI());
        this.mockAPIBtn.addEventListener('click', () => this.mockAPI());
        
        // Collaboration Events
        this.shareProjectBtn.addEventListener('click', () => this.shareProject());
        this.liveCodeBtn.addEventListener('click', () => this.startLiveCode());
        this.codeReviewRequestBtn.addEventListener('click', () => this.requestCodeReview());
        this.teamChatBtn.addEventListener('click', () => this.openTeamChat());
        this.versionControlBtn.addEventListener('click', () => this.openVersionControl());
        
        // Package Management Events
        this.installPackageBtn.addEventListener('click', () => this.installPackage());
        this.listPackagesBtn.addEventListener('click', () => this.listPackages());
        this.suggestPackagesBtn.addEventListener('click', () => this.suggestPackages());
        this.updatePackagesBtn.addEventListener('click', () => this.updatePackages());
        this.createRequirementsBtn.addEventListener('click', () => this.createRequirements());
        
        // Performance Events
        this.profileCodeBtn.addEventListener('click', () => this.profileCode());
        this.memoryUsageBtn.addEventListener('click', () => this.analyzeMemoryUsage());
        this.optimizePerformanceBtn.addEventListener('click', () => this.optimizePerformance());
        this.benchmarkBtn.addEventListener('click', () => this.benchmarkCode());
        this.resourceMonitorBtn.addEventListener('click', () => this.monitorResources());
        
        // Template Events
        this.webAppTemplateBtn.addEventListener('click', () => this.loadTemplate('web_app'));
        this.mlProjectTemplateBtn.addEventListener('click', () => this.loadTemplate('ml_project'));
        this.apiProjectTemplateBtn.addEventListener('click', () => this.loadTemplate('api_project'));
        this.desktopAppTemplateBtn.addEventListener('click', () => this.loadTemplate('desktop_app'));
        this.dataAnalysisTemplateBtn.addEventListener('click', () => this.loadTemplate('data_analysis'));
        this.gameDevTemplateBtn.addEventListener('click', () => this.loadTemplate('game_dev'));
        
        // Quality Events
        this.codeQualityBtn.addEventListener('click', () => this.analyzeCodeQuality());
        this.testCoverageBtn.addEventListener('click', () => this.analyzeTestCoverage());
        this.codeSmellsBtn.addEventListener('click', () => this.detectCodeSmells());
        this.duplicateCodeBtn.addEventListener('click', () => this.findDuplicateCode());
        this.maintainabilityBtn.addEventListener('click', () => this.analyzeMaintainability());
        
        // Deployment Events
        this.deployToCloudBtn.addEventListener('click', () => this.deployToCloud());
        this.dockerizeBtn.addEventListener('click', () => this.dockerizeApp());
        this.cicdSetupBtn.addEventListener('click', () => this.setupCICD());
        this.deployToHerokuBtn.addEventListener('click', () => this.deployToHeroku());
        this.createExecutableBtn.addEventListener('click', () => this.createExecutable());
        
        // New Speaker Events
        document.getElementById('speakAnalysisBtn').addEventListener('click', () => this.speakSection('speakAnalysisBtn'));
        document.getElementById('speakMLBtn').addEventListener('click', () => this.speakSection('speakMLBtn'));
        document.getElementById('speakDBBtn').addEventListener('click', () => this.speakSection('speakDBBtn'));
        document.getElementById('speakAPIBtn').addEventListener('click', () => this.speakSection('speakAPIBtn'));
        document.getElementById('speakCollabBtn').addEventListener('click', () => this.speakSection('speakCollabBtn'));
        document.getElementById('speakPackageBtn').addEventListener('click', () => this.speakSection('speakPackageBtn'));
        document.getElementById('speakPerformanceBtn').addEventListener('click', () => this.speakSection('speakPerformanceBtn'));
        document.getElementById('speakTemplatesBtn').addEventListener('click', () => this.speakSection('speakTemplatesBtn'));
        document.getElementById('speakQualityBtn').addEventListener('click', () => this.speakSection('speakQualityBtn'));
        document.getElementById('speakDeploymentBtn').addEventListener('click', () => this.speakSection('speakDeploymentBtn'));
        
        // Speaker button events
        document.getElementById('speakGeneratorBtn').addEventListener('click', () => this.speakSection('speakGeneratorBtn'));
        document.getElementById('speakProjectBtn').addEventListener('click', () => this.speakSection('speakProjectBtn'));
        document.getElementById('speakEditorBtn').addEventListener('click', () => this.speakSection('speakEditorBtn'));
        document.getElementById('speakExecutionBtn').addEventListener('click', () => this.speakSection('speakExecutionBtn'));
        document.getElementById('speakAiBtn').addEventListener('click', () => this.speakSection('speakAiBtn'));
        document.getElementById('speakLearningBtn').addEventListener('click', () => this.speakSection('speakLearningBtn'));
        document.getElementById('speakGithubBtn').addEventListener('click', () => this.speakSection('speakGithubBtn'));
        
        // Line numbers update
        this.codeEditor.addEventListener('input', () => {
            this.updateLineNumbers();
            this.showAutocomplete();
        });
        
        this.codeEditor.addEventListener('scroll', () => {
            this.lineNumbers.scrollTop = this.codeEditor.scrollTop;
        });

        // Close autocomplete when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.editor-wrapper')) {
                this.autocompletePopup.style.display = 'none';
            }
        });
    }

    initializeSpeechSynthesis() {
        this.updateSpeedLabel();
        this.pauseAudioBtn.disabled = true;
        this.stopAudioBtn.disabled = true;
    }

    // File Operations
    loadFile(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.name.endsWith('.py')) {
            this.showError('يرجى اختيار ملف بايثون (.py)');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.codeEditor.value = e.target.result;
            this.currentCode = e.target.result;
            this.addToTerminal(`تم تحميل الملف: ${file.name}`, 'success');
        };
        reader.readAsText(file);
    }

    saveFile() {
        if (!this.currentCode) {
            this.showError('لا يوجد كود للحفظ');
            return;
        }

        const blob = new Blob([this.currentCode], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'code.py';
        a.click();
        URL.revokeObjectURL(url);
        
        this.addToTerminal('تم حفظ الملف بنجاح', 'success');
    }

    clearEditor() {
        this.codeEditor.value = '';
        this.currentCode = '';
        this.addToTerminal('تم مسح الكود', 'success');
    }

    // Code Execution
    async runCode() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('يرجى كتابة بعض الكود أولاً');
            return;
        }

        this.setRunningStatus(true);
        this.addToTerminal('بدء تشغيل الكود...', 'success');
        
        try {
            // Simulate code execution
            await this.simulateCodeExecution(code);
        } catch (error) {
            this.addToTerminal(`خطأ في التشغيل: ${error.message}`, 'error');
        } finally {
            this.setRunningStatus(false);
        }
    }

    async simulateCodeExecution(code) {
        // Simulate execution delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Simple code analysis and simulation
        const lines = code.split('\n');
        let output = [];
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.startsWith('print(')) {
                const match = trimmed.match(/print\((.*)\)/);
                if (match) {
                    const content = match[1].replace(/['"]/g, '');
                    output.push(content);
                }
            }
        }
        
        if (output.length > 0) {
            output.forEach(line => this.addToTerminal(line, 'success'));
        } else {
            this.addToTerminal('تم تنفيذ الكود بنجاح', 'success');
        }
        
        // Check for common patterns
        if (code.includes('def ')) {
            this.addToTerminal('تم تعريف دالة جديدة', 'success');
        }
        if (code.includes('class ')) {
            this.addToTerminal('تم تعريف فئة جديدة', 'success');
        }
    }

    clearOutput() {
        this.terminalOutput.innerHTML = '';
        this.addToTerminal('تم مسح الناتج', 'success');
    }

    setRunningStatus(running) {
        this.isRunning = running;
        this.runCodeBtn.disabled = running;
        this.runningIndicator.className = running ? 'indicator running' : 'indicator';
        this.statusText.textContent = running ? 'قيد التشغيل...' : 'جاهز';
    }

    addToTerminal(message, type = 'normal') {
        const outputLine = document.createElement('div');
        outputLine.className = `output-line ${type}`;
        outputLine.textContent = `> ${message}`;
        this.terminalOutput.appendChild(outputLine);
        this.terminalOutput.scrollTop = this.terminalOutput.scrollHeight;
    }

    // AI Operations
    async explainCode() {
        await this.performAIOperation('explain', 'شرح الكود');
    }

    async fixErrors() {
        await this.performAIOperation('fix', 'إصلاح الأخطاء');
    }

    async optimizeCode() {
        await this.performAIOperation('optimize', 'تحسين الكود');
    }

    async generateTests() {
        await this.performAIOperation('test', 'إنشاء اختبارات');
    }

    async securityCheck() {
        await this.performAIOperation('security', 'فحص أمني');
    }

    async performAIOperation(operation, operationName) {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('يرجى كتابة بعض الكود أولاً');
            return;
        }

        this.showLoading(`جاري ${operationName}...`);
        
        try {
            const response = await this.callAIService(operation, code);
            this.displayAIResponse(response);
            this.currentAiResponse = response;
        } catch (error) {
            this.showError(`خطأ في ${operationName}: ${error.message}`);
        } finally {
            this.hideLoading();
        }
    }

    async callAIService(operation, code) {
        // Simulate AI processing
        await new Promise(resolve => setTimeout(resolve, 2000));

        const responses = {
            explain: this.generateExplanation(code),
            fix: this.generateFixSuggestions(code),
            optimize: this.generateOptimizations(code),
            test: this.generateTests(code),
            security: this.generateSecurityAnalysis(code)
        };

        return responses[operation] || 'تم تحليل الكود بنجاح';
    }

    generateExplanation(code) {
        const lines = code.split('\n').filter(line => line.trim());
        let explanation = "شرح الكود:\n\n";

        if (code.includes('def ')) {
            explanation += "• يحتوي الكود على تعريف دوال (functions)\n";
        }
        if (code.includes('class ')) {
            explanation += "• يحتوي الكود على تعريف فئات (classes)\n";
        }
        if (code.includes('for ') || code.includes('while ')) {
            explanation += "• يستخدم الكود حلقات تكرار (loops)\n";
        }
        if (code.includes('if ')) {
            explanation += "• يحتوي على جمل شرطية (conditional statements)\n";
        }
        if (code.includes('import ')) {
            explanation += "• يستورد مكتبات خارجية\n";
        }
        if (code.includes('print(')) {
            explanation += "• يطبع نتائج على الشاشة\n";
        }

        explanation += "\nالغرض العام: هذا الكود يبدو أنه مكتوب بلغة Python ويحتوي على عناصر برمجية أساسية.";

        return explanation;
    }

    generateFixSuggestions(code) {
        let suggestions = "اقتراحات الإصلاح:\n\n";
        const lines = code.split('\n');

        // Check for common issues
        if (!code.includes('def main()') && code.length > 50) {
            suggestions += "• يُنصح بإضافة دالة main() لتنظيم الكود\n";
        }

        // Check indentation
        let hasIndentationIssues = false;
        for (let line of lines) {
            if (line.trim() && !line.startsWith(' ') && !line.startsWith('\t') &&
                (line.includes(':') || lines.indexOf(line) > 0)) {
                hasIndentationIssues = true;
                break;
            }
        }

        if (hasIndentationIssues) {
            suggestions += "• تحقق من المسافات البادئة (indentation)\n";
        }

        if (!code.includes('try:') && code.includes('input(')) {
            suggestions += "• يُنصح بإضافة معالجة للأخطاء (try-except)\n";
        }

        suggestions += "\nالكود يبدو جيداً بشكل عام. تأكد من اتباع أفضل الممارسات في Python.";

        return suggestions;
    }

    generateOptimizations(code) {
        let optimizations = "اقتراحات التحسين:\n\n";

        if (code.includes('for i in range(len(')) {
            optimizations += "• استخدم enumerate() بدلاً من range(len())\n";
        }

        if (code.includes('list.append') && code.includes('for ')) {
            optimizations += "• فكر في استخدام list comprehension للأداء الأفضل\n";
        }

        if (code.includes('open(') && !code.includes('with ')) {
            optimizations += "• استخدم with statement لإدارة الملفات بشكل أفضل\n";
        }

        optimizations += "• أضف تعليقات توضيحية للكود\n";
        optimizations += "• استخدم أسماء متغيرات واضحة ومعبرة\n";
        optimizations += "• قسم الكود إلى دوال صغيرة ومتخصصة\n";

        return optimizations;
    }

    generateTests(code) {
        let tests = "اختبارات مقترحة:\n\n";

        tests += "```python\n";
        tests += "import unittest\n\n";
        tests += "class TestCode(unittest.TestCase):\n";
        tests += "    def test_basic_functionality(self):\n";
        tests += "        # اختبار الوظيفة الأساسية\n";
        tests += "        pass\n\n";
        tests += "    def test_edge_cases(self):\n";
        tests += "        # اختبار الحالات الحدية\n";
        tests += "        pass\n\n";
        tests += "    def test_error_handling(self):\n";
        tests += "        # اختبار معالجة الأخطاء\n";
        tests += "        pass\n\n";
        tests += "if __name__ == '__main__':\n";
        tests += "    unittest.main()\n";
        tests += "```\n\n";
        tests += "تأكد من اختبار جميع الحالات المحتملة والحالات الاستثنائية.";

        return tests;
    }

    generateSecurityAnalysis(code) {
        let analysis = "تحليل أمني:\n\n";

        if (code.includes('input(')) {
            analysis += "⚠️ تحقق من صحة المدخلات من المستخدم\n";
        }

        if (code.includes('eval(') || code.includes('exec(')) {
            analysis += "🚨 تجنب استخدام eval() أو exec() - خطر أمني عالي\n";
        }

        if (code.includes('open(') && code.includes('w')) {
            analysis += "⚠️ تأكد من صلاحيات الكتابة على الملفات\n";
        }

        if (code.includes('import os') || code.includes('subprocess')) {
            analysis += "⚠️ كن حذراً عند تنفيذ أوامر النظام\n";
        }

        analysis += "\n✅ نصائح أمنية عامة:\n";
        analysis += "• تحقق من صحة جميع المدخلات\n";
        analysis += "• استخدم مكتبات موثوقة فقط\n";
        analysis += "• لا تخزن كلمات المرور في النص الواضح\n";
        analysis += "• استخدم HTTPS للاتصالات الشبكية\n";

        return analysis;
    }

    displayAIResponse(response) {
        this.aiResponse.innerHTML = `
            <div class="ai-response-content">
                <div class="ai-icon">🤖</div>
                <div style="white-space: pre-wrap;">${response}</div>
            </div>
        `;
    }

    // Audio Operations
    updateSpeedLabel() {
        const speed = parseFloat(this.speedRange.value);
        this.speedLabel.textContent = `${speed.toFixed(1)}x`;
    }

    async playAudio() {
        if (!this.currentAiResponse) {
            this.showError('لا يوجد نص للتشغيل. يرجى تحليل الكود أولاً');
            return;
        }

        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio = null;
        }

        this.setAudioControlsState(true);

        try {
            const voice = this.voiceSelect.value;
            const speed = parseFloat(this.speedRange.value);

            // Clean text from code symbols for better speech
            const cleanText = this.cleanTextForSpeech(this.currentAiResponse);

            // Use Web Speech API if available
            if ('speechSynthesis' in window) {
                this.speakText(cleanText, voice, speed);
            } else {
                this.showError('متصفحك لا يدعم تشغيل الصوت');
                this.setAudioControlsState(false);
            }

        } catch (error) {
            console.error('خطأ في إنشاء الصوت:', error);
            this.showError('حدث خطأ في إنشاء الصوت');
            this.setAudioControlsState(false);
        }
    }

    speakText(text, voice, speed) {
        const utterance = new SpeechSynthesisUtterance(text);

        // Set voice if available
        const voices = speechSynthesis.getVoices();
        const arabicVoice = voices.find(v => v.lang.includes('ar')) || voices[0];
        if (arabicVoice) {
            utterance.voice = arabicVoice;
        }

        utterance.rate = speed;
        utterance.lang = 'ar-SA';

        utterance.onstart = () => {
            this.playAudioBtn.innerHTML = '🔊 يتم التشغيل...';
            this.pauseAudioBtn.disabled = false;
            this.stopAudioBtn.disabled = false;

            // Display audio player
            this.audioPlayer.innerHTML = `
                <div style="text-align: center; color: #667eea;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">🎵</div>
                    <p>يتم تشغيل الصوت...</p>
                </div>
            `;
        };

        utterance.onend = () => {
            this.setAudioControlsState(false);
            this.audioPlayer.innerHTML = `
                <div style="text-align: center; color: #666;">
                    <div style="font-size: 2rem; margin-bottom: 10px;">🔊</div>
                    <p>انتهى التشغيل</p>
                </div>
            `;
        };

        utterance.onerror = (error) => {
            console.error('خطأ في تشغيل الصوت:', error);
            this.showError('حدث خطأ في تشغيل الصوت');
            this.setAudioControlsState(false);
        };

        speechSynthesis.speak(utterance);
        this.currentAudio = utterance;
    }

    pauseAudio() {
        if (speechSynthesis.speaking) {
            if (speechSynthesis.paused) {
                speechSynthesis.resume();
                this.pauseAudioBtn.innerHTML = '⏸️ إيقاف مؤقت';
            } else {
                speechSynthesis.pause();
                this.pauseAudioBtn.innerHTML = '▶️ متابعة';
            }
        }
    }

    stopAudio() {
        if (speechSynthesis.speaking) {
            speechSynthesis.cancel();
        }
        this.currentAudio = null;
        this.setAudioControlsState(false);
        this.audioPlayer.innerHTML = `
            <div style="text-align: center; color: #666;">
                <div style="font-size: 2rem; margin-bottom: 10px;">🔊</div>
                <p>جاهز للتشغيل</p>
            </div>
        `;
    }

    setAudioControlsState(playing) {
        this.playAudioBtn.disabled = playing;
        this.pauseAudioBtn.disabled = !playing;
        this.stopAudioBtn.disabled = !playing;
        
        if (!playing) {
            this.playAudioBtn.innerHTML = '🔊 تشغيل الصوت';
            this.pauseAudioBtn.innerHTML = '⏸️ إيقاف مؤقت';
        }
    }

    cleanTextForSpeech(text) {
        // Remove code blocks and technical symbols for better speech
        return text
            .replace(/```[\s\S]*?```/g, ' كود برمجي ')
            .replace(/`[^`]+`/g, ' كود ')
            .replace(/def\s+\w+\s*\(/g, 'تعريف دالة ')
            .replace(/class\s+\w+/g, 'تعريف فئة ')
            .replace(/import\s+\w+/g, 'استيراد مكتبة ')
            .replace(/print\s*\(/g, 'طباعة ')
            .replace(/if\s+/g, 'إذا كان ')
            .replace(/else:/g, 'وإلا ')
            .replace(/for\s+/g, 'لكل ')
            .replace(/while\s+/g, 'بينما ')
            .replace(/return\s+/g, 'إرجاع ')
            .replace(/\{[\s\S]*?\}/g, ' ')
            .replace(/\[[\s\S]*?\]/g, ' ')
            .replace(/[{}[\]()]/g, ' ')
            .replace(/[#@$%^&*+=|\\:";'<>?,./]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
    }

    // AI Code Generation
    async generateCode() {
        const prompt = this.codePrompt.value.trim();
        if (!prompt) {
            this.showError('يرجى كتابة وصف للكود المطلوب');
            return;
        }

        this.showLoading('جاري إنشاء الكود...');

        try {
            // Simulate code generation based on prompt
            const generatedCode = this.generateCodeFromPrompt(prompt);
            this.displayGeneratedCode(generatedCode);
            this.currentAiResponse = generatedCode;

        } catch (error) {
            console.error('خطأ في إنشاء الكود:', error);
            this.showError('حدث خطأ في إنشاء الكود');
        } finally {
            this.hideLoading();
        }
    }

    generateCodeFromPrompt(prompt) {
        const lowerPrompt = prompt.toLowerCase();

        if (lowerPrompt.includes('حاسبة') || lowerPrompt.includes('calculator')) {
            return this.generateCalculatorCode();
        } else if (lowerPrompt.includes('فاتورة') || lowerPrompt.includes('ضريبة')) {
            return this.generateInvoiceCode();
        } else if (lowerPrompt.includes('فرز') || lowerPrompt.includes('ترتيب')) {
            return this.generateSortingCode();
        } else if (lowerPrompt.includes('طلاب') || lowerPrompt.includes('students')) {
            return this.generateStudentManagementCode();
        } else if (lowerPrompt.includes('api') || lowerPrompt.includes('fastapi')) {
            return this.generateAPICode();
        } else if (lowerPrompt.includes('تعلم آلة') || lowerPrompt.includes('machine learning')) {
            return this.generateMLCode();
        } else if (lowerPrompt.includes('قاعدة بيانات') || lowerPrompt.includes('database')) {
            return this.generateDatabaseCode();
        } else {
            return this.generateBasicCode(prompt);
        }
    }

    generateCalculatorCode() {
        return `# حاسبة بسيطة
def calculator():
    """حاسبة تدعم العمليات الأساسية"""
    print("=== حاسبة بسيطة ===")

    while True:
        try:
            # إدخال الأرقام
            num1 = float(input("أدخل الرقم الأول: "))
            operation = input("أدخل العملية (+, -, *, /): ")
            num2 = float(input("أدخل الرقم الثاني: "))

            # تنفيذ العملية
            if operation == '+':
                result = num1 + num2
            elif operation == '-':
                result = num1 - num2
            elif operation == '*':
                result = num1 * num2
            elif operation == '/':
                if num2 != 0:
                    result = num1 / num2
                else:
                    print("خطأ: لا يمكن القسمة على صفر")
                    continue
            else:
                print("عملية غير صحيحة")
                continue

            print(f"النتيجة: {num1} {operation} {num2} = {result}")

            # السؤال عن المتابعة
            if input("هل تريد المتابعة؟ (y/n): ").lower() != 'y':
                break

        except ValueError:
            print("خطأ: يرجى إدخال أرقام صحيحة")
        except Exception as e:
            print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    calculator()`;
    }

    generateInvoiceCode() {
        return `# حساب الفاتورة مع ضريبة القيمة المضافة
def calculate_invoice():
    """حساب الفاتورة مع ضريبة القيمة المضافة"""
    print("=== حساب الفاتورة ===")

    # إعدادات الضريبة
    VAT_RATE = 0.15  # 15% ضريبة القيمة المضافة

    items = []
    total_before_tax = 0

    while True:
        try:
            item_name = input("اسم المنتج (أو 'انتهاء' للإنهاء): ")
            if item_name.lower() == 'انتهاء':
                break

            quantity = int(input("الكمية: "))
            price = float(input("السعر للوحدة: "))

            item_total = quantity * price
            items.append({
                'name': item_name,
                'quantity': quantity,
                'price': price,
                'total': item_total
            })

            total_before_tax += item_total

        except ValueError:
            print("خطأ: يرجى إدخال قيم صحيحة")

    # حساب الضريبة والإجمالي
    vat_amount = total_before_tax * VAT_RATE
    total_after_tax = total_before_tax + vat_amount

    # طباعة الفاتورة
    print("\\n" + "="*40)
    print("           الفاتورة النهائية")
    print("="*40)

    for item in items:
        print(f"{item['name']}: {item['quantity']} × {item['price']:.2f} = {item['total']:.2f}")

    print("-"*40)
    print(f"المجموع قبل الضريبة: {total_before_tax:.2f}")
    print(f"ضريبة القيمة المضافة ({VAT_RATE*100}%): {vat_amount:.2f}")
    print(f"المجموع النهائي: {total_after_tax:.2f}")
    print("="*40)

if __name__ == "__main__":
    calculate_invoice()`;
    }

    generateSortingCode() {
        return `# خوارزميات الفرز المختلفة
def bubble_sort(arr):
    """فرز الفقاعة - Bubble Sort"""
    n = len(arr)
    for i in range(n):
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
    return arr

def quick_sort(arr):
    """الفرز السريع - Quick Sort"""
    if len(arr) <= 1:
        return arr

    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]

    return quick_sort(left) + middle + quick_sort(right)

def merge_sort(arr):
    """فرز الدمج - Merge Sort"""
    if len(arr) <= 1:
        return arr

    mid = len(arr) // 2
    left = merge_sort(arr[:mid])
    right = merge_sort(arr[mid:])

    return merge(left, right)

def merge(left, right):
    """دمج قائمتين مرتبتين"""
    result = []
    i = j = 0

    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1

    result.extend(left[i:])
    result.extend(right[j:])
    return result

# مثال على الاستخدام
if __name__ == "__main__":
    numbers = [64, 34, 25, 12, 22, 11, 90]
    print(f"القائمة الأصلية: {numbers}")

    # اختبار فرز الفقاعة
    bubble_result = bubble_sort(numbers.copy())
    print(f"فرز الفقاعة: {bubble_result}")

    # اختبار الفرز السريع
    quick_result = quick_sort(numbers.copy())
    print(f"الفرز السريع: {quick_result}")

    # اختبار فرز الدمج
    merge_result = merge_sort(numbers.copy())
    print(f"فرز الدمج: {merge_result}")`;
    }

    generateStudentManagementCode() {
        return `# نظام إدارة الطلاب
class Student:
    """فئة الطالب"""
    def __init__(self, student_id, name, age, grade):
        self.student_id = student_id
        self.name = name
        self.age = age
        self.grade = grade
        self.courses = []

    def add_course(self, course):
        """إضافة مقرر للطالب"""
        if course not in self.courses:
            self.courses.append(course)
            print(f"تم إضافة المقرر {course} للطالب {self.name}")

    def remove_course(self, course):
        """حذف مقرر من الطالب"""
        if course in self.courses:
            self.courses.remove(course)
            print(f"تم حذف المقرر {course} من الطالب {self.name}")

    def get_info(self):
        """الحصول على معلومات الطالب"""
        return {
            'id': self.student_id,
            'name': self.name,
            'age': self.age,
            'grade': self.grade,
            'courses': self.courses
        }

class StudentManager:
    """مدير نظام الطلاب"""
    def __init__(self):
        self.students = {}

    def add_student(self, student_id, name, age, grade):
        """إضافة طالب جديد"""
        if student_id not in self.students:
            student = Student(student_id, name, age, grade)
            self.students[student_id] = student
            print(f"تم إضافة الطالب {name} بنجاح")
            return True
        else:
            print("الطالب موجود بالفعل")
            return False

    def remove_student(self, student_id):
        """حذف طالب"""
        if student_id in self.students:
            name = self.students[student_id].name
            del self.students[student_id]
            print(f"تم حذف الطالب {name}")
            return True
        else:
            print("الطالب غير موجود")
            return False

    def find_student(self, student_id):
        """البحث عن طالب"""
        return self.students.get(student_id)

    def list_all_students(self):
        """عرض جميع الطلاب"""
        if not self.students:
            print("لا يوجد طلاب مسجلون")
            return

        print("قائمة الطلاب:")
        for student in self.students.values():
            info = student.get_info()
            print(f"ID: {info['id']}, الاسم: {info['name']}, العمر: {info['age']}, الصف: {info['grade']}")

    def get_students_by_grade(self, grade):
        """الحصول على طلاب صف معين"""
        return [s for s in self.students.values() if s.grade == grade]

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء مدير الطلاب
    manager = StudentManager()

    # إضافة طلاب
    manager.add_student("001", "أحمد محمد", 16, "الصف العاشر")
    manager.add_student("002", "فاطمة علي", 15, "الصف التاسع")
    manager.add_student("003", "محمد أحمد", 17, "الصف الحادي عشر")

    # عرض جميع الطلاب
    manager.list_all_students()

    # البحث عن طالب
    student = manager.find_student("001")
    if student:
        student.add_course("الرياضيات")
        student.add_course("الفيزياء")
        print(f"مقررات {student.name}: {student.courses}")

    print("نظام إدارة الطلاب جاهز!")`;
    }

    generateBasicCode(prompt) {
        return `# كود Python أساسي
# بناءً على الطلب: ${prompt}

def main():
    """الدالة الرئيسية للبرنامج"""
    print("مرحباً! هذا برنامج Python أساسي")
    print(f"تم إنشاؤه بناءً على: {prompt}")

    # يمكنك إضافة الكود الخاص بك هنا
    user_input = input("أدخل شيئاً: ")
    print(f"لقد أدخلت: {user_input}")

    # مثال على حلقة تكرار
    for i in range(5):
        print(f"العدد: {i + 1}")

    print("انتهى البرنامج!")

if __name__ == "__main__":
    main()`;
    }

    displayGeneratedCode(code) {
        this.generatedCode.innerHTML = `
            <div class="generated-code-content">
                <div class="code-header">
                    <h3>الكود المولود</h3>
                    <div class="code-actions">
                        <button class="btn btn-small btn-primary" onclick="assistant.copyToEditor()">📋 نسخ للمحرر</button>
                        <button class="btn btn-small btn-secondary" onclick="assistant.copyToClipboard()">📄 نسخ للحافظة</button>
                    </div>
                </div>
                <pre><code>${this.escapeHtml(code)}</code></pre>
            </div>
        `;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    copyToEditor() {
        const codeMatch = this.currentAiResponse.match(/```python\n([\s\S]*?)\n```/);
        if (codeMatch) {
            this.codeEditor.value = codeMatch[1];
            this.currentCode = codeMatch[1];
            this.updateLineNumbers();
            this.addToTerminal('تم نسخ الكود إلى المحرر بنجاح', 'success');
        } else {
            this.codeEditor.value = this.currentAiResponse;
            this.currentCode = this.currentAiResponse;
            this.updateLineNumbers();
            this.addToTerminal('تم نسخ المحتوى إلى المحرر', 'success');
        }
    }

    copyToClipboard() {
        navigator.clipboard.writeText(this.currentAiResponse).then(() => {
            this.addToTerminal('تم نسخ الكود إلى الحافظة', 'success');
        }).catch(() => {
            this.showError('فشل في نسخ الكود');
        });
    }

    // Project Management
    newProject() {
        if (confirm('هل أنت متأكد من إنشاء مشروع جديد؟ سيتم فقدان التغييرات غير المحفوظة.')) {
            this.projectFiles = { 'main.py': '' };
            this.activeFile = 'main.py';
            this.codeEditor.value = '';
            this.currentCode = '';
            this.clearOutput();
            this.updateFilesList();
            this.updateLineNumbers();
            this.addToTerminal('تم إنشاء مشروع جديد', 'success');
        }
    }

    saveProject() {
        this.projectFiles[this.activeFile] = this.codeEditor.value;
        const projectData = {
            name: 'مشروع Python',
            files: this.projectFiles,
            activeFile: this.activeFile,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'python-project.json';
        a.click();
        URL.revokeObjectURL(url);
        
        this.addToTerminal('تم حفظ المشروع بنجاح', 'success');
    }

    loadProject() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const projectData = JSON.parse(e.target.result);
                        this.projectFiles = projectData.files;
                        this.activeFile = projectData.activeFile || 'main.py';
                        this.codeEditor.value = this.projectFiles[this.activeFile] || '';
                        this.currentCode = this.codeEditor.value;
                        this.updateFilesList();
                        this.updateLineNumbers();
                        this.addToTerminal(`تم تحميل المشروع: ${projectData.name}`, 'success');
                    } catch (error) {
                        this.showError('خطأ في تحميل المشروع');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    exportProject() {
        this.projectFiles[this.activeFile] = this.codeEditor.value;
        
        // Simple export as JSON since JSZip is not available
        const projectData = {
            name: 'مشروع Python',
            files: this.projectFiles,
            activeFile: this.activeFile,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'python-project-export.json';
        a.click();
        URL.revokeObjectURL(url);
        
        this.addToTerminal('تم تصدير المشروع بنجاح كملف JSON', 'success');
    }

    addFile() {
        const filename = prompt('اسم الملف الجديد (مع الامتداد):');
        if (filename && filename.trim()) {
            const trimmedName = filename.trim();
            if (this.projectFiles[trimmedName]) {
                this.showError('الملف موجود بالفعل');
                return;
            }
            this.projectFiles[trimmedName] = '';
            this.updateFilesList();
            this.switchToFile(trimmedName);
            this.addToTerminal(`تم إضافة الملف: ${trimmedName}`, 'success');
        }
    }

    switchToFile(filename) {
        this.projectFiles[this.activeFile] = this.codeEditor.value;
        this.activeFile = filename;
        this.codeEditor.value = this.projectFiles[filename] || '';
        this.currentCode = this.codeEditor.value;
        this.updateFilesList();
        this.updateLineNumbers();
    }

    updateFilesList() {
        const container = document.getElementById('projectFiles');
        container.innerHTML = '';
        
        for (const filename of Object.keys(this.projectFiles)) {
            const fileItem = document.createElement('div');
            fileItem.className = `file-item ${filename === this.activeFile ? 'active' : ''}`;
            fileItem.innerHTML = `
                <span class="file-icon">${this.getFileIcon(filename)}</span>
                <span class="file-name">${filename}</span>
                <button class="btn btn-small btn-secondary" onclick="assistant.deleteFile('${filename}')">🗑️</button>
            `;
            fileItem.onclick = (e) => {
                if (e.target.tagName !== 'BUTTON') {
                    this.switchToFile(filename);
                }
            };
            container.appendChild(fileItem);
        }
    }

    deleteFile(filename) {
        if (filename === 'main.py') {
            this.showError('لا يمكن حذف الملف الرئيسي');
            return;
        }
        
        if (confirm(`هل أنت متأكد من حذف الملف: ${filename}؟`)) {
            delete this.projectFiles[filename];
            if (this.activeFile === filename) {
                this.activeFile = 'main.py';
                this.codeEditor.value = this.projectFiles['main.py'];
                this.currentCode = this.codeEditor.value;
            }
            this.updateFilesList();
            this.updateLineNumbers();
            this.addToTerminal(`تم حذف الملف: ${filename}`, 'success');
        }
    }

    getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const icons = {
            'py': '🐍',
            'txt': '📄',
            'md': '📝',
            'json': '📋',
            'html': '🌐',
            'css': '🎨',
            'js': '⚡'
        };
        return icons[ext] || '📄';
    }

    // Enhanced Code Features
    formatCode() {
        const code = this.codeEditor.value;
        if (!code.trim()) {
            this.showError('لا يوجد كود للتنسيق');
            return;
        }
        
        // Simple Python formatting
        const formatted = this.formatPythonCode(code);
        this.codeEditor.value = formatted;
        this.currentCode = formatted;
        this.updateLineNumbers();
        this.addToTerminal('تم تنسيق الكود بنجاح', 'success');
    }

    formatPythonCode(code) {
        const lines = code.split('\n');
        let formatted = [];
        let indentLevel = 0;
        
        for (let line of lines) {
            const trimmed = line.trim();
            if (!trimmed) {
                formatted.push('');
                continue;
            }
            
            if (trimmed.startsWith('except:') || trimmed.startsWith('else:') || 
                trimmed.startsWith('elif ') || trimmed.startsWith('finally:')) {
                indentLevel = Math.max(0, indentLevel - 1);
            }
            
            formatted.push('    '.repeat(indentLevel) + trimmed);
            
            if (trimmed.endsWith(':')) {
                indentLevel++;
            }
        }
        
        return formatted.join('\n');
    }

    async debugCode() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('لا يوجد كود للتتبع');
            return;
        }

        this.showLoading('جاري تتبع الأخطاء...');

        try {
            // Simulate debugging analysis
            await new Promise(resolve => setTimeout(resolve, 2000));
            const debugResult = this.analyzeCodeForDebugging(code);
            this.displayAIResponse(debugResult);
            this.currentAiResponse = debugResult;

        } catch (error) {
            console.error('خطأ في تتبع الأخطاء:', error);
            this.showError('حدث خطأ في تتبع الأخطاء');
        } finally {
            this.hideLoading();
        }
    }

    analyzeCodeForDebugging(code) {
        let analysis = "تحليل الكود للأخطاء:\n\n";
        const lines = code.split('\n');
        let issues = [];

        // Check for common Python issues
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const lineNum = i + 1;

            // Check for syntax issues
            if (line.includes('print ') && !line.includes('print(')) {
                issues.push(`السطر ${lineNum}: استخدم print() بدلاً من print في Python 3`);
            }

            if (line.includes('=') && !line.includes('==') && (line.includes('if ') || line.includes('while '))) {
                issues.push(`السطر ${lineNum}: ربما تقصد == للمقارنة بدلاً من = للإسناد`);
            }

            if (line.includes('input()') && !line.includes('int(') && !line.includes('float(')) {
                issues.push(`السطر ${lineNum}: input() يُرجع نص، استخدم int() أو float() للأرقام`);
            }

            if (line.includes('range(') && line.includes('len(')) {
                issues.push(`السطر ${lineNum}: فكر في استخدام enumerate() بدلاً من range(len())`);
            }
        }

        if (issues.length > 0) {
            analysis += "🔍 المشاكل المحتملة:\n";
            issues.forEach(issue => analysis += `• ${issue}\n`);
        } else {
            analysis += "✅ لم يتم العثور على مشاكل واضحة في الكود\n";
        }

        analysis += "\n💡 نصائح عامة للتتبع:\n";
        analysis += "• استخدم print() لطباعة قيم المتغيرات\n";
        analysis += "• تحقق من أنواع البيانات باستخدام type()\n";
        analysis += "• استخدم try-except لمعالجة الأخطاء\n";
        analysis += "• تأكد من المسافات البادئة (indentation)\n";

        return analysis;
    }

    stepByStepExecution() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('لا يوجد كود للتنفيذ');
            return;
        }
        
        this.simulateStepByStep(code);
    }

    simulateStepByStep(code) {
        const lines = code.split('\n').filter(line => line.trim());
        this.trackerContent.innerHTML = '';
        
        lines.forEach((line, index) => {
            const trackerItem = document.createElement('div');
            trackerItem.className = 'tracker-item';
            trackerItem.innerHTML = `
                <span class="line-indicator">السطر ${index + 1}: ${line.trim()}</span>
                <span class="status ready">في الانتظار</span>
            `;
            this.trackerContent.appendChild(trackerItem);
        });
        
        this.executeStepByStep(0);
    }

    async executeStepByStep(currentStep) {
        const trackerItems = this.trackerContent.querySelectorAll('.tracker-item');
        if (currentStep >= trackerItems.length) return;
        
        const currentItem = trackerItems[currentStep];
        const statusElement = currentItem.querySelector('.status');
        
        statusElement.textContent = 'قيد التنفيذ';
        statusElement.className = 'status running';
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        statusElement.textContent = 'مكتمل';
        statusElement.className = 'status completed';
        
        setTimeout(() => {
            this.executeStepByStep(currentStep + 1);
        }, 500);
    }

    // Auto-complete functionality
    setupAutoComplete() {
        this.pythonKeywords = [
            'def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except',
            'finally', 'import', 'from', 'return', 'yield', 'lambda', 'with',
            'as', 'pass', 'break', 'continue', 'and', 'or', 'not', 'is', 'in',
            'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'tuple', 'set'
        ];
    }

    showAutocomplete() {
        const cursorPos = this.codeEditor.selectionStart;
        const textBeforeCursor = this.codeEditor.value.substring(0, cursorPos);
        const words = textBeforeCursor.split(/\s+/);
        const currentWord = words[words.length - 1];
        
        if (currentWord.length < 2) {
            this.autocompletePopup.style.display = 'none';
            return;
        }
        
        const suggestions = this.pythonKeywords.filter(keyword => 
            keyword.startsWith(currentWord.toLowerCase())
        );
        
        if (suggestions.length === 0) {
            this.autocompletePopup.style.display = 'none';
            return;
        }
        
        this.autocompletePopup.innerHTML = suggestions.map(suggestion => 
            `<div class="autocomplete-item" onclick="assistant.selectAutoComplete('${suggestion}')">${suggestion}</div>`
        ).join('');
        
        // Position the popup
        const rect = this.codeEditor.getBoundingClientRect();
        this.autocompletePopup.style.left = '20px';
        this.autocompletePopup.style.top = '100px';
        this.autocompletePopup.style.display = 'block';
    }

    selectAutoComplete(suggestion) {
        const cursorPos = this.codeEditor.selectionStart;
        const textBeforeCursor = this.codeEditor.value.substring(0, cursorPos);
        const textAfterCursor = this.codeEditor.value.substring(cursorPos);
        const words = textBeforeCursor.split(/\s+/);
        const currentWord = words[words.length - 1];
        
        const newTextBefore = textBeforeCursor.substring(0, textBeforeCursor.length - currentWord.length);
        this.codeEditor.value = newTextBefore + suggestion + textAfterCursor;
        this.codeEditor.selectionStart = this.codeEditor.selectionEnd = newTextBefore.length + suggestion.length;
        
        this.autocompletePopup.style.display = 'none';
        this.codeEditor.focus();
    }

    updateLineNumbers() {
        const lines = this.codeEditor.value.split('\n');
        const lineNumbersText = lines.map((_, index) => index + 1).join('\n');
        this.lineNumbers.textContent = lineNumbersText;
    }

    // Learning Resources
    async showPythonDocs() {
        this.showLoading('جاري تحميل المراجع...');
        
        const docsContent = `
            <div class="docs-content">
                <h3>مرجع Python السريع</h3>
                <div class="docs-section">
                    <h4>أساسيات Python</h4>
                    <ul>
                        <li><strong>المتغيرات:</strong> x = 5, name = "أحمد"</li>
                        <li><strong>القوائم:</strong> numbers = [1, 2, 3, 4]</li>
                        <li><strong>القواميس:</strong> person = {"name": "أحمد", "age": 25}</li>
                        <li><strong>الدوال:</strong> def my_function(parameter):</li>
                    </ul>
                </div>
                <div class="docs-section">
                    <h4>هياكل التحكم</h4>
                    <ul>
                        <li><strong>الشروط:</strong> if condition: / elif: / else:</li>
                        <li><strong>حلقات التكرار:</strong> for item in list: / while condition:</li>
                        <li><strong>معالجة الأخطاء:</strong> try: / except: / finally:</li>
                    </ul>
                </div>
                <div class="docs-section">
                    <h4>دوال مفيدة</h4>
                    <ul>
                        <li><strong>الطباعة:</strong> print("Hello World")</li>
                        <li><strong>الطول:</strong> len(my_list)</li>
                        <li><strong>الفرز:</strong> sorted(my_list)</li>
                        <li><strong>التحويل:</strong> int(x), str(x), float(x)</li>
                    </ul>
                </div>
            </div>
        `;
        
        this.learningContent.innerHTML = docsContent;
        this.currentAiResponse = "مرجع Python السريع مع الأساسيات والدوال المفيدة";
        this.hideLoading();
    }

    async showTutorials() {
        this.showLoading('جاري تحميل الدروس...');
        
        const tutorialsContent = `
            <div class="tutorials-content">
                <h3>دروس تعليمية تفاعلية</h3>
                <div class="tutorial-item">
                    <h4>الدرس الأول: مرحبا بالعالم</h4>
                    <p>تعلم كيفية كتابة برنامجك الأول</p>
                    <button class="btn btn-primary" onclick="assistant.loadTutorial('hello_world')">ابدأ الدرس</button>
                </div>
                <div class="tutorial-item">
                    <h4>الدرس الثاني: المتغيرات والبيانات</h4>
                    <p>فهم أنواع البيانات والمتغيرات في Python</p>
                    <button class="btn btn-primary" onclick="assistant.loadTutorial('variables')">ابدأ الدرس</button>
                </div>
                <div class="tutorial-item">
                    <h4>الدرس الثالث: الشروط والحلقات</h4>
                    <p>تعلم كيفية استخدام if/else و for/while</p>
                    <button class="btn btn-primary" onclick="assistant.loadTutorial('control_flow')">ابدأ الدرس</button>
                </div>
                <div class="tutorial-item">
                    <h4>الدرس الرابع: الدوال</h4>
                    <p>إنشاء واستخدام الدوال في Python</p>
                    <button class="btn btn-primary" onclick="assistant.loadTutorial('functions')">ابدأ الدرس</button>
                </div>
            </div>
        `;
        
        this.learningContent.innerHTML = tutorialsContent;
        this.currentAiResponse = "دروس تعليمية تفاعلية لتعلم Python خطوة بخطوة";
        this.hideLoading();
    }

    async showExamples() {
        this.showLoading('جاري تحميل الأمثلة...');
        
        const examplesContent = `
            <div class="examples-content">
                <h3>أمثلة عملية</h3>
                <div class="example-item">
                    <h4>حاسبة بسيطة</h4>
                    <button class="btn btn-primary" onclick="assistant.loadExample('calculator')">تحميل المثال</button>
                </div>
                <div class="example-item">
                    <h4>إدارة قائمة المهام</h4>
                    <button class="btn btn-primary" onclick="assistant.loadExample('todo_list')">تحميل المثال</button>
                </div>
                <div class="example-item">
                    <h4>لعبة تخمين الرقم</h4>
                    <button class="btn btn-primary" onclick="assistant.loadExample('guessing_game')">تحميل المثال</button>
                </div>
                <div class="example-item">
                    <h4>تحليل النصوص</h4>
                    <button class="btn btn-primary" onclick="assistant.loadExample('text_analyzer')">تحميل المثال</button>
                </div>
            </div>
        `;
        
        this.learningContent.innerHTML = examplesContent;
        this.currentAiResponse = "أمثلة عملية ومشاريع صغيرة لتطبيق ما تعلمته";
        this.hideLoading();
    }

    async showChallenges() {
        this.showLoading('جاري تحميل التحديات...');
        
        const challengesContent = `
            <div class="challenges-content">
                <h3>تحديات برمجية</h3>
                <div class="challenge-item">
                    <h4>تحدي المبتدئين: فرز الأرقام</h4>
                    <p>اكتب برنامج لفرز قائمة من الأرقام</p>
                    <button class="btn btn-primary" onclick="assistant.loadChallenge('sort_numbers')">قبول التحدي</button>
                </div>
                <div class="challenge-item">
                    <h4>تحدي المتوسطين: البحث في النص</h4>
                    <p>أنشئ برنامج للبحث عن كلمة في نص</p>
                    <button class="btn btn-primary" onclick="assistant.loadChallenge('text_search')">قبول التحدي</button>
                </div>
                <div class="challenge-item">
                    <h4>تحدي المتقدمين: نظام إدارة الملفات</h4>
                    <p>طور نظام بسيط لإدارة الملفات</p>
                    <button class="btn btn-primary" onclick="assistant.loadChallenge('file_manager')">قبول التحدي</button>
                </div>
            </div>
        `;
        
        this.learningContent.innerHTML = challengesContent;
        this.currentAiResponse = "تحديات برمجية متدرجة لتطوير مهاراتك";
        this.hideLoading();
    }

    loadTutorial(tutorialId) {
        const tutorials = {
            'hello_world': `# الدرس الأول: مرحبا بالعالم
print("مرحبا بالعالم!")
print("أهلاً وسهلاً بك في عالم البرمجة")

# جرب تغيير الرسالة
name = "أحمد"
print(f"مرحبا {name}!")`,
            
            'variables': `# الدرس الثاني: المتغيرات والبيانات
# الأرقام الصحيحة
age = 25
print(f"العمر: {age}")

# الأرقام العشرية
height = 175.5
print(f"الطول: {height} سم")

# النصوص
name = "سارة"
print(f"الاسم: {name}")

# القوائم
hobbies = ["القراءة", "الرياضة", "البرمجة"]
print(f"الهوايات: {hobbies}")`,
            
            'control_flow': `# الدرس الثالث: الشروط والحلقات
# الشروط
age = 18
if age >= 18:
    print("بالغ")
else:
    print("قاصر")

# حلقة for
numbers = [1, 2, 3, 4, 5]
for num in numbers:
    print(f"الرقم: {num}")

# حلقة while
count = 0
while count < 3:
    print(f"العداد: {count}")
    count += 1`,
            
            'functions': `# الدرس الرابع: الدوال
def greet(name):
    """دالة للترحيب بالمستخدم"""
    return f"مرحبا {name}!"

def calculate_area(length, width):
    """دالة لحساب المساحة"""
    return length * width

# استخدام الدوال
message = greet("أحمد")
print(message)

area = calculate_area(5, 3)
print(f"المساحة: {area}")`
        };
        
        if (tutorials[tutorialId]) {
            this.codeEditor.value = tutorials[tutorialId];
            this.currentCode = tutorials[tutorialId];
            this.updateLineNumbers();
            this.addToTerminal(`تم تحميل الدرس: ${tutorialId}`, 'success');
        }
    }

    loadExample(exampleId) {
        const examples = {
            'calculator': `# حاسبة بسيطة
def add(x, y):
    return x + y

def subtract(x, y):
    return x - y

def multiply(x, y):
    return x * y

def divide(x, y):
    if y != 0:
        return x / y
    else:
        return "خطأ: القسمة على صفر"

# أمثلة على الاستخدام
print(f"5 + 3 = {add(5, 3)}")
print(f"10 - 4 = {subtract(10, 4)}")
print(f"6 * 7 = {multiply(6, 7)}")
print(f"15 / 3 = {divide(15, 3)}")`,
            
            'todo_list': `# إدارة قائمة المهام
tasks = []

def add_task(task):
    tasks.append(task)
    print(f"تم إضافة المهمة: {task}")

def remove_task(task):
    if task in tasks:
        tasks.remove(task)
        print(f"تم حذف المهمة: {task}")
    else:
        print("المهمة غير موجودة")

def show_tasks():
    print("قائمة المهام:")
    for i, task in enumerate(tasks, 1):
        print(f"{i}. {task}")

# استخدام النظام
add_task("دراسة البرمجة")
add_task("ممارسة الرياضة")
add_task("قراءة كتاب")
show_tasks()`,
            
            'guessing_game': `# لعبة تخمين الرقم
import random

def guessing_game():
    number = random.randint(1, 100)
    attempts = 0
    
    print("مرحبا بك في لعبة تخمين الرقم!")
    print("اخترت رقم بين 1 و 100")
    
    while True:
        try:
            guess = int(input("خمن الرقم: "))
            attempts += 1
            
            if guess == number:
                print(f"مبروك! الرقم الصحيح هو {number}")
                print(f"عدد المحاولات: {attempts}")
                break
            elif guess < number:
                print("الرقم أكبر")
            else:
                print("الرقم أصغر")
        except ValueError:
            print("يرجى إدخال رقم صحيح")

# بدء اللعبة
guessing_game()`,
            
            'text_analyzer': `# تحليل النصوص
def analyze_text(text):
    """تحليل النص وإرجاع إحصائيات"""
    words = text.split()
    
    stats = {
        'character_count': len(text),
        'word_count': len(words),
        'sentence_count': text.count('.') + text.count('!') + text.count('?'),
        'paragraph_count': text.count('\\n\\n') + 1
    }
    
    return stats

def find_common_words(text, top_n=5):
    """العثور على الكلمات الأكثر شيوعاً"""
    words = text.lower().split()
    word_count = {}
    
    for word in words:
        word_count[word] = word_count.get(word, 0) + 1
    
    return sorted(word_count.items(), key=lambda x: x[1], reverse=True)[:top_n]

# مثال على الاستخدام
sample_text = "Python is amazing. Python is powerful. I love Python programming."
stats = analyze_text(sample_text)
print("إحصائيات النص:")
for key, value in stats.items():
    print(f"{key}: {value}")

common_words = find_common_words(sample_text)
print("\\nالكلمات الأكثر شيوعاً:")
for word, count in common_words:
    print(f"{word}: {count}")`
        };
        
        if (examples[exampleId]) {
            this.codeEditor.value = examples[exampleId];
            this.currentCode = examples[exampleId];
            this.updateLineNumbers();
            this.addToTerminal(`تم تحميل المثال: ${exampleId}`, 'success');
        }
    }

    loadChallenge(challengeId) {
        const challenges = {
            'sort_numbers': `# تحدي: فرز الأرقام
# المطلوب: اكتب دالة لفرز قائمة من الأرقام
# بدون استخدام sorted() أو .sort()

def bubble_sort(numbers):
    """
    اكتب هنا الكود لفرز القائمة
    استخدم خوارزمية الفقاعات أو أي طريقة أخرى
    """
    # كودك هنا
    pass

# اختبار الدالة
test_numbers = [64, 34, 25, 12, 22, 11, 90]
print(f"القائمة الأصلية: {test_numbers}")
sorted_numbers = bubble_sort(test_numbers.copy())
print(f"القائمة المرتبة: {sorted_numbers}")`,
            
            'text_search': `# تحدي: البحث في النص
# المطلوب: أنشئ برنامج للبحث عن كلمة في نص

def search_word(text, word):
    """
    البحث عن كلمة في النص
    يجب أن ترجع موقع الكلمة وعدد مرات ظهورها
    """
    # كودك هنا
    pass

def highlight_word(text, word):
    """
    تمييز الكلمة في النص
    ضع الكلمة بين علامات ** **
    """
    # كودك هنا
    pass

# اختبار البرنامج
sample_text = "Python is a programming language. Python is easy to learn."
search_word_input = "Python"
print(f"البحث عن: {search_word_input}")
print(f"في النص: {sample_text}")`,
            
            'file_manager': `# تحدي: نظام إدارة الملفات
# المطلوب: طور نظام بسيط لإدارة الملفات

class FileManager:
    def __init__(self):
        self.files = {}  # اسم الملف: محتوى الملف
    
    def create_file(self, filename, content=""):
        """إنشاء ملف جديد"""
        # كودك هنا
        pass
    
    def read_file(self, filename):
        """قراءة محتوى الملف"""
        # كودك هنا
        pass
    
    def write_file(self, filename, content):
        """كتابة محتوى في الملف"""
        # كودك هنا
        pass
    
    def delete_file(self, filename):
        """حذف الملف"""
        # كودك هنا
        pass
    
    def list_files(self):
        """عرض قائمة الملفات"""
        # كودك هنا
        pass

# اختبار النظام
fm = FileManager()
fm.create_file("test.txt", "Hello World")
print(fm.read_file("test.txt"))
fm.list_files()`
        };
        
        if (challenges[challengeId]) {
            this.codeEditor.value = challenges[challengeId];
            this.currentCode = challenges[challengeId];
            this.updateLineNumbers();
            this.addToTerminal(`تم تحميل التحدي: ${challengeId}`, 'success');
        }
    }

    // AI Chat System
    openAiChat() {
        this.aiChatModal.style.display = 'flex';
        this.chatInput.focus();
    }

    closeAiChat() {
        this.aiChatModal.style.display = 'none';
    }

    async sendChatMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;

        // Add user message to chat
        this.addChatMessage(message, 'user');
        this.chatInput.value = '';

        // Add to conversation history
        this.conversationHistory.push({
            role: "user",
            content: message
        });

        // Show loading
        this.showLoading('جاري المعالجة...');

        try {
            // Simulate AI response
            await new Promise(resolve => setTimeout(resolve, 1500));
            const aiResponse = this.generateChatResponse(message);

            // Add AI response to chat
            this.addChatMessage(aiResponse, 'ai');

            // Add to conversation history
            this.conversationHistory.push({
                role: "assistant",
                content: aiResponse
            });

        } catch (error) {
            console.error('خطأ في الدردشة:', error);
            this.addChatMessage('عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.', 'ai');
        } finally {
            this.hideLoading();
        }
    }

    generateChatResponse(message) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('مرحبا') || lowerMessage.includes('السلام')) {
            return 'مرحباً بك! أنا مساعدك البرمجي الذكي. كيف يمكنني مساعدتك في البرمجة اليوم؟';
        }

        if (lowerMessage.includes('python') || lowerMessage.includes('بايثون')) {
            return 'Python لغة برمجة رائعة! هي سهلة التعلم وقوية جداً. يمكنني مساعدتك في تعلم أساسياتها أو حل مشاكل محددة. ما الذي تريد معرفته؟';
        }

        if (lowerMessage.includes('خطأ') || lowerMessage.includes('error')) {
            return 'لا تقلق من الأخطاء! هي جزء طبيعي من التعلم. يمكنني مساعدتك في فهم الخطأ وإصلاحه. شارك معي الكود والخطأ الذي تواجهه.';
        }

        if (lowerMessage.includes('تعلم') || lowerMessage.includes('learn')) {
            return 'رائع! أحب مساعدة المتعلمين الجدد. يمكنني تقديم دروس تفاعلية وأمثلة عملية. من أين تريد أن نبدأ؟ المتغيرات؟ الحلقات؟ الدوال؟';
        }

        if (lowerMessage.includes('مشروع') || lowerMessage.includes('project')) {
            return 'المشاريع العملية أفضل طريقة للتعلم! يمكنني مساعدتك في إنشاء مشاريع مثل: حاسبة، لعبة بسيطة، تطبيق إدارة المهام، أو تحليل البيانات. ما نوع المشروع الذي يهمك؟';
        }

        if (lowerMessage.includes('دالة') || lowerMessage.includes('function')) {
            return 'الدوال في Python تُعرّف باستخدام def. مثال:\n\ndef greet(name):\n    return f"مرحبا {name}!"\n\nهل تريد أمثلة أكثر أو مساعدة في دالة محددة؟';
        }

        if (lowerMessage.includes('حلقة') || lowerMessage.includes('loop')) {
            return 'الحلقات مفيدة جداً! في Python لدينا:\n- for loop للتكرار على العناصر\n- while loop للتكرار بشرط\n\nمثال:\nfor i in range(5):\n    print(i)\n\nهل تريد أمثلة أكثر؟';
        }

        // Default response
        return 'شكراً لسؤالك! أنا هنا لمساعدتك في البرمجة. يمكنني مساعدتك في:\n• شرح مفاهيم Python\n• حل الأخطاء البرمجية\n• مراجعة الكود\n• اقتراح مشاريع\n• تقديم أمثلة عملية\n\nما الذي تحتاج مساعدة فيه تحديداً؟';
    }

    addChatMessage(message, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${sender}-message`;
        
        const avatar = sender === 'user' ? '👤' : '🤖';
        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">${message}</div>
        `;
        
        this.chatContainer.appendChild(messageDiv);
        this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
    }

    // Settings Management
    openSettings() {
        this.settingsModal.style.display = 'flex';
    }

    closeSettings() {
        this.settingsModal.style.display = 'none';
    }

    updateSettings() {
        this.settings.voice = document.getElementById('voiceSelect').value;
        this.settings.speed = parseFloat(document.getElementById('speedRange').value);
        this.settings.volume = parseFloat(document.getElementById('volumeRange').value);
        this.settings.fontSize = parseInt(document.getElementById('fontSizeRange').value);
        this.settings.theme = document.getElementById('themeSelect').value;
        
        document.getElementById('speedLabel').textContent = `${this.settings.speed.toFixed(1)}x`;
        document.getElementById('volumeLabel').textContent = `${Math.round(this.settings.volume * 100)}%`;
        document.getElementById('fontSizeLabel').textContent = `${this.settings.fontSize}px`;
        
        this.applySettings();
        this.saveSettings();
    }

    applySettings() {
        this.codeEditor.style.fontSize = `${this.settings.fontSize}px`;
        document.body.className = `theme-${this.settings.theme}`;
    }

    saveSettings() {
        localStorage.setItem('codingAssistantSettings', JSON.stringify(this.settings));
    }

    loadSettings() {
        const saved = localStorage.getItem('codingAssistantSettings');
        if (saved) {
            this.settings = {...this.settings, ...JSON.parse(saved)};
            this.voiceSelect.value = this.settings.voice;
            this.speedRange.value = this.settings.speed;
            this.volumeRange.value = this.settings.volume;
            this.fontSizeRange.value = this.settings.fontSize;
            this.themeSelect.value = this.settings.theme;
            this.updateSettings();
        }
    }

    // GitHub Integration Methods
    openGithubModal() {
        this.githubModal.style.display = 'flex';
        this.repoUrlInput.focus();
    }

    closeGithubModal() {
        this.githubModal.style.display = 'none';
        this.searchResults.style.display = 'none';
        this.repoUrlInput.value = '';
        this.repoSearchInput.value = '';
    }

    async loadRepository() {
        const repoUrl = this.repoUrlInput.value.trim();
        if (!repoUrl) {
            this.showError('يرجى إدخال رابط المستودع');
            return;
        }

        // Parse GitHub URL
        const repoMatch = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
        if (!repoMatch) {
            this.showError('رابط غير صحيح. يرجى إدخال رابط GitHub صحيح');
            return;
        }

        const owner = repoMatch[1];
        const repo = repoMatch[2].replace('.git', '');

        this.showLoading('جاري تحميل المستودع...');
        
        try {
            // Simulate GitHub API calls
            const repoData = await this.fetchRepositoryData(owner, repo);
            this.currentRepository = repoData;
            this.displayRepositoryInfo(repoData);
            await this.loadRepositoryFiles(owner, repo);
            this.closeGithubModal();
            this.addToTerminal(`تم تحميل المستودع: ${owner}/${repo}`, 'success');
        } catch (error) {
            console.error('خطأ في تحميل المستودع:', error);
            this.showError('فشل في تحميل المستودع. تأكد من صحة الرابط');
        } finally {
            this.hideLoading();
        }
    }

    async fetchRepositoryData(owner, repo) {
        // Simulate GitHub API response
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return {
            name: repo,
            owner: owner,
            description: `مستودع ${repo} من ${owner}`,
            stars: Math.floor(Math.random() * 1000),
            forks: Math.floor(Math.random() * 100),
            language: 'Python',
            url: `https://github.com/${owner}/${repo}`,
            size: Math.floor(Math.random() * 10000) + 'KB'
        };
    }

    displayRepositoryInfo(repoData) {
        this.repoInfo.innerHTML = `
            <div class="repo-details">
                <h3>${repoData.owner}/${repoData.name}</h3>
                <p>${repoData.description}</p>
                <div class="repo-stats">
                    <div class="repo-stat">
                        <span>⭐ النجوم:</span>
                        <span>${repoData.stars}</span>
                    </div>
                    <div class="repo-stat">
                        <span>🍴 الفروع:</span>
                        <span>${repoData.forks}</span>
                    </div>
                    <div class="repo-stat">
                        <span>📝 اللغة:</span>
                        <span>${repoData.language}</span>
                    </div>
                    <div class="repo-stat">
                        <span>📊 الحجم:</span>
                        <span>${repoData.size}</span>
                    </div>
                </div>
            </div>
        `;
    }

    async loadRepositoryFiles(owner, repo) {
        // Simulate fetching repository files
        await new Promise(resolve => setTimeout(resolve, 500));
        
        this.repositoryFiles = [
            { name: 'main.py', type: 'file', path: 'main.py', size: '1.2KB' },
            { name: 'README.md', type: 'file', path: 'README.md', size: '2.1KB' },
            { name: 'requirements.txt', type: 'file', path: 'requirements.txt', size: '0.5KB' },
            { name: 'src', type: 'folder', path: 'src', children: [
                { name: 'utils.py', type: 'file', path: 'src/utils.py', size: '3.4KB' },
                { name: 'models.py', type: 'file', path: 'src/models.py', size: '5.2KB' },
                { name: 'config.py', type: 'file', path: 'src/config.py', size: '0.8KB' }
            ]},
            { name: 'tests', type: 'folder', path: 'tests', children: [
                { name: 'test_main.py', type: 'file', path: 'tests/test_main.py', size: '2.3KB' },
                { name: 'test_utils.py', type: 'file', path: 'tests/test_utils.py', size: '1.8KB' }
            ]},
            { name: 'docs', type: 'folder', path: 'docs', children: [
                { name: 'index.md', type: 'file', path: 'docs/index.md', size: '1.5KB' },
                { name: 'api.md', type: 'file', path: 'docs/api.md', size: '4.2KB' }
            ]}
        ];
        
        this.displayFileTree();
    }

    displayFileTree() {
        this.fileTree.innerHTML = '';
        this.renderFiles(this.repositoryFiles, this.fileTree, 0);
    }

    renderFiles(files, container, level) {
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = `file-item-tree ${file.type}`;
            fileItem.style.paddingLeft = `${20 + level * 20}px`;
            
            const icon = this.getFileTreeIcon(file);
            fileItem.innerHTML = `
                <span class="file-icon">${icon}</span>
                <span class="file-name">${file.name}</span>
                ${file.size ? `<span class="file-size">(${file.size})</span>` : ''}
            `;
            
            if (file.type === 'file') {
                fileItem.onclick = () => this.openRepositoryFile(file);
            } else if (file.type === 'folder') {
                fileItem.onclick = () => this.toggleFolder(fileItem, file);
            }
            
            container.appendChild(fileItem);
            
            if (file.children && file.type === 'folder') {
                const childContainer = document.createElement('div');
                childContainer.className = 'folder-children';
                childContainer.style.display = 'none';
                this.renderFiles(file.children, childContainer, level + 1);
                container.appendChild(childContainer);
            }
        });
    }

    getFileTreeIcon(file) {
        if (file.type === 'folder') {
            return '📁';
        }
        
        const ext = file.name.split('.').pop().toLowerCase();
        const icons = {
            'py': '🐍',
            'md': '📝',
            'txt': '📄',
            'json': '📋',
            'yml': '⚙️',
            'yaml': '⚙️',
            'js': '⚡',
            'html': '🌐',
            'css': '🎨',
            'jpg': '🖼️',
            'png': '🖼️',
            'gif': '🖼️'
        };
        
        return icons[ext] || '📄';
    }

    toggleFolder(folderElement, folderData) {
        const childContainer = folderElement.nextElementSibling;
        if (childContainer && childContainer.className === 'folder-children') {
            const isVisible = childContainer.style.display !== 'none';
            childContainer.style.display = isVisible ? 'none' : 'block';
            
            const icon = folderElement.querySelector('.file-icon');
            icon.textContent = isVisible ? '📁' : '📂';
        }
    }

    async openRepositoryFile(file) {
        this.showLoading('جاري تحميل الملف...');
        
        try {
            const content = await this.fetchFileContent(file.path);
            this.currentFileContent = content;
            this.displayFileContent(file, content);
        } catch (error) {
            console.error('خطأ في تحميل الملف:', error);
            this.showError('فشل في تحميل الملف');
        } finally {
            this.hideLoading();
        }
    }

    async fetchFileContent(filePath) {
        // Simulate fetching file content
        await new Promise(resolve => setTimeout(resolve, 800));
        
        const sampleContents = {
            'main.py': `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البرنامج الرئيسي
"""

def main():
    """الدالة الرئيسية للبرنامج"""
    print("مرحباً بك في البرنامج!")
    
    # إعداد البيانات
    data = initialize_data()
    
    # معالجة البيانات
    result = process_data(data)
    
    # عرض النتائج
    display_results(result)

def initialize_data():
    """تهيئة البيانات الأساسية"""
    return {
        'users': [],
        'settings': {
            'language': 'ar',
            'theme': 'dark'
        }
    }

def process_data(data):
    """معالجة البيانات"""
    # تنفيذ المعالجة هنا
    return data

def display_results(result):
    """عرض النتائج"""
    print(f"النتائج: {result}")

if __name__ == "__main__":
    main()`,
            'README.md': `# مشروع Python

## الوصف
هذا مشروع تطبيق Python لإدارة البيانات والمعالجة.

## المتطلبات
- Python 3.8+
- pandas
- numpy
- requests

## التثبيت
\`\`\`bash
pip install -r requirements.txt
\`\`\`

## الاستخدام
\`\`\`python
python main.py
\`\`\`

## الميزات
- إدارة البيانات
- معالجة الملفات
- واجهة سهلة الاستخدام

## المساهمة
مرحب بالمساهمات! يرجى إنشاء Pull Request.

## الترخيص
MIT License`,
            'requirements.txt': `pandas==1.5.3
numpy==1.24.3
requests==2.31.0
matplotlib==3.7.1
seaborn==0.12.2
flask==2.3.2
sqlalchemy==2.0.15`,
            'src/utils.py': `# -*- coding: utf-8 -*-
"""
أدوات مساعدة للمشروع
"""

import os
import json
import datetime

def load_config(config_path='config.json'):
    """تحميل ملف الإعدادات"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def save_config(config, config_path='config.json'):
    """حفظ الإعدادات"""
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

def get_timestamp():
    """الحصول على الوقت الحالي"""
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def format_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"`,
            'tests/test_main.py': `# -*- coding: utf-8 -*-
"""
اختبارات الوحدة للبرنامج الرئيسي
"""

import unittest
import sys
import os

# إضافة مسار المصدر
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import initialize_data, process_data

class TestMain(unittest.TestCase):
    """اختبارات الوظائف الرئيسية"""
    
    def test_initialize_data(self):
        """اختبار تهيئة البيانات"""
        data = initialize_data()
        self.assertIsInstance(data, dict)
        self.assertIn('users', data)
        self.assertIn('settings', data)
    
    def test_process_data(self):
        """اختبار معالجة البيانات"""
        test_data = {'test': 'value'}
        result = process_data(test_data)
        self.assertEqual(result, test_data)
    
    def test_settings_structure(self):
        """اختبار هيكل الإعدادات"""
        data = initialize_data()
        settings = data['settings']
        self.assertIn('language', settings)
        self.assertIn('theme', settings)
        self.assertEqual(settings['language'], 'ar')

if __name__ == '__main__':
    unittest.main()`
        };
        
        return sampleContents[filePath] || `# محتوى الملف: ${filePath}
# هذا محتوى عينة للملف المحدد

def example_function():
    """مثال على دالة"""
    print("مرحباً من الملف: ${filePath}")
    return True

if __name__ == "__main__":
    example_function()`;
    }

    displayFileContent(file, content) {
        this.fileModalTitle.textContent = file.name;
        this.fileContentCode.textContent = content;
        this.fileContentModal.style.display = 'flex';
    }

    closeFileModal() {
        this.fileContentModal.style.display = 'none';
    }

    runSelectedFile() {
        if (this.currentFileContent) {
            this.codeEditor.value = this.currentFileContent;
            this.currentCode = this.currentFileContent;
            this.updateLineNumbers();
            this.closeFileModal();
            this.runCode();
            this.addToTerminal('تم تشغيل الملف من المستودع', 'success');
        }
    }

    copySelectedFile() {
        if (this.currentFileContent) {
            this.codeEditor.value = this.currentFileContent;
            this.currentCode = this.currentFileContent;
            this.updateLineNumbers();
            this.closeFileModal();
            this.addToTerminal('تم نسخ الملف إلى المحرر', 'success');
        }
    }

    downloadSelectedFile() {
        if (this.currentFileContent) {
            const blob = new Blob([this.currentFileContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = this.fileModalTitle.textContent;
            a.click();
            URL.revokeObjectURL(url);
            this.addToTerminal('تم تحميل الملف', 'success');
        }
    }

    async searchRepositories() {
        const query = this.repoSearchInput.value.trim();
        if (!query) {
            this.showError('يرجى إدخال كلمة البحث');
            return;
        }

        this.showLoading('جاري البحث في المستودعات...');
        
        try {
            const results = await this.fetchRepositorySearchResults(query);
            this.displaySearchResults(results);
        } catch (error) {
            console.error('خطأ في البحث:', error);
            this.showError('فشل في البحث');
        } finally {
            this.hideLoading();
        }
    }

    async fetchRepositorySearchResults(query) {
        // Simulate GitHub search API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return [
            {
                name: `python-${query}`,
                owner: 'developer1',
                description: `مشروع Python متقدم لـ ${query}`,
                stars: 245,
                forks: 52,
                language: 'Python',
                url: `https://github.com/developer1/python-${query}`
            },
            {
                name: `${query}-tutorial`,
                owner: 'educator',
                description: `دروس تعليمية لـ ${query}`,
                stars: 189,
                forks: 34,
                language: 'Python',
                url: `https://github.com/educator/${query}-tutorial`
            },
            {
                name: `awesome-${query}`,
                owner: 'community',
                description: `قائمة رائعة بموارد ${query}`,
                stars: 1023,
                forks: 156,
                language: 'Python',
                url: `https://github.com/community/awesome-${query}`
            }
        ];
    }

    displaySearchResults(results) {
        this.searchResults.innerHTML = '';
        
        if (results.length === 0) {
            this.searchResults.innerHTML = '<p style="padding: 20px; text-align: center; color: #666;">لا توجد نتائج</p>';
        } else {
            results.forEach(repo => {
                const item = document.createElement('div');
                item.className = 'search-result-item';
                item.innerHTML = `
                    <div class="repo-name">${repo.owner}/${repo.name}</div>
                    <div class="repo-description">${repo.description}</div>
                    <div class="repo-meta">
                        <span>⭐ ${repo.stars}</span>
                        <span>🍴 ${repo.forks}</span>
                        <span>📝 ${repo.language}</span>
                    </div>
                `;
                item.onclick = () => {
                    this.repoUrlInput.value = repo.url;
                    this.loadRepository();
                };
                this.searchResults.appendChild(item);
            });
        }
        
        this.searchResults.style.display = 'block';
    }

    filterFiles() {
        const searchTerm = this.fileSearchInput.value.toLowerCase();
        const fileItems = this.fileTree.querySelectorAll('.file-item-tree');
        
        fileItems.forEach(item => {
            const fileName = item.querySelector('.file-name').textContent.toLowerCase();
            const isMatch = fileName.includes(searchTerm);
            item.style.display = isMatch || searchTerm === '' ? 'flex' : 'none';
        });
    }

    async searchInRepository() {
        if (!this.currentRepository) {
            this.showError('لا يوجد مستودع محمل');
            return;
        }

        const searchTerm = prompt('أدخل كلمة البحث:');
        if (!searchTerm) return;

        this.showLoading('جاري البحث في المستودع...');
        
        try {
            const results = await this.searchInRepositoryFiles(searchTerm);
            this.displaySearchInRepoResults(results, searchTerm);
        } catch (error) {
            console.error('خطأ في البحث:', error);
            this.showError('فشل في البحث');
        } finally {
            this.hideLoading();
        }
    }

    async searchInRepositoryFiles(searchTerm) {
        // Simulate searching in repository files
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const results = [];
        const searchFiles = (files) => {
            files.forEach(file => {
                if (file.type === 'file' && file.name.toLowerCase().includes(searchTerm.toLowerCase())) {
                    results.push({
                        file: file,
                        matches: [`السطر 1: مطابقة لـ "${searchTerm}"`]
                    });
                }
                if (file.children) {
                    searchFiles(file.children);
                }
            });
        };
        
        searchFiles(this.repositoryFiles);
        return results;
    }

    displaySearchInRepoResults(results, searchTerm) {
        let resultText = `نتائج البحث عن "${searchTerm}":\n\n`;
        
        if (results.length === 0) {
            resultText += 'لا توجد نتائج';
        } else {
            results.forEach(result => {
                resultText += `📄 ${result.file.name} (${result.file.path})\n`;
                result.matches.forEach(match => {
                    resultText += `   ${match}\n`;
                });
                resultText += '\n';
            });
        }
        
        this.displayAIResponse(resultText);
        this.currentAiResponse = resultText;
    }

    async cloneRepository() {
        if (!this.currentRepository) {
            this.showError('لا يوجد مستودع محمل');
            return;
        }

        this.showLoading('جاري استنساخ المستودع...');
        
        try {
            // Simulate cloning process
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Add all files to project
            const addFilesToProject = (files) => {
                files.forEach(file => {
                    if (file.type === 'file') {
                        this.projectFiles[file.path] = `# تم استنساخ من ${this.currentRepository.owner}/${this.currentRepository.name}\n# الملف: ${file.path}\n\n# محتوى الملف سيكون هنا`;
                    }
                    if (file.children) {
                        addFilesToProject(file.children);
                    }
                });
            };
            
            addFilesToProject(this.repositoryFiles);
            this.updateFilesList();
            this.addToTerminal(`تم استنساخ المستودع: ${this.currentRepository.owner}/${this.currentRepository.name}`, 'success');
        } catch (error) {
            console.error('خطأ في الاستنساخ:', error);
            this.showError('فشل في استنساخ المستودع');
        } finally {
            this.hideLoading();
        }
    }

    async refreshRepository() {
        if (!this.currentRepository) {
            this.showError('لا يوجد مستودع محمل');
            return;
        }

        this.showLoading('جاري تحديث المستودع...');
        
        try {
            await this.loadRepositoryFiles(this.currentRepository.owner, this.currentRepository.name);
            this.addToTerminal('تم تحديث المستودع بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في التحديث:', error);
            this.showError('فشل في تحديث المستودع');
        } finally {
            this.hideLoading();
        }
    }

    // Enhanced Audio Features
    async speakSection(buttonId) {
        let textToSpeak = '';
        
        switch(buttonId) {
            case 'speakGeneratorBtn':
                textToSpeak = this.currentAiResponse || 'مولد الكود الذكي جاهز لإنشاء الكود حسب وصفك. اكتب وصفاً للكود الذي تريده وسأقوم بإنشائه لك.';
                break;
            case 'speakProjectBtn':
                textToSpeak = `إدارة المشروع. الملفات المتاحة: ${Object.keys(this.projectFiles).join(', ')}. الملف النشط حالياً: ${this.activeFile}`;
                break;
            case 'speakEditorBtn':
                textToSpeak = this.codeEditor.value || 'محرر الكود المتقدم فارغ. ابدأ بكتابة الكود الخاص بك هنا.';
                break;
            case 'speakExecutionBtn':
                const outputText = this.terminalOutput.textContent;
                textToSpeak = outputText || 'الطرفية جاهزة. قم بتشغيل الكود لرؤية النتائج.';
                break;
            case 'speakAiBtn':
                textToSpeak = this.currentAiResponse || 'مساعد الذكاء الاصطناعي المتقدم جاهز لمساعدتك. اختر أي من الخيارات لتحليل وتطوير الكود الخاص بك.';
                break;
            case 'speakLearningBtn':
                const learningText = this.learningContent.textContent;
                textToSpeak = learningText || 'قسم التعلم والمراجع. اختر من القائمة للوصول إلى المواد التعليمية والمراجع والأمثلة العملية.';
                break;
            case 'speakGithubBtn':
                if (this.currentRepository) {
                    textToSpeak = `تكامل مستودع GitHub. المستودع المحمل حالياً: ${this.currentRepository.owner}/${this.currentRepository.name}. يمكنك تصفح الملفات والبحث داخل المستودع.`;
                } else {
                    textToSpeak = 'تكامل مستودع GitHub. لا يوجد مستودع محمل حالياً. يمكنك إضافة مستودع جديد أو البحث في المستودعات العامة.';
                }
                break;
            case 'speakAnalysisBtn':
                textToSpeak = this.currentAiResponse || 'قسم تحليل الكود المتقدم. يمكنك تحليل تعقيد الكود وأدائه ومراجعته والبحث عن الأخطاء والحصول على اقتراحات للتحسين.';
                break;
            case 'speakMLBtn':
                textToSpeak = this.currentAiResponse || 'قسم التعلم الآلي المتكامل. يمكنك إنشاء نموذج التعلم الآلي وتدريبها وإجراء التنبؤات وتقييم الأداء وتصور البيانات.';
                break;
            case 'speakDBBtn':
                textToSpeak = this.currentAiResponse || 'قسم إدارة قواعد البيانات. يمكنك الاتصال بقواعد البيانات وإنشاء الجداول وبناء الاستعلامات والترحيل وإنشاء النسخ الاحتياطية.';
                break;
            case 'speakAPIBtn':
                textToSpeak = this.currentAiResponse || 'قسم اختبار واجهات API. يمكنك إنشاء اختبارات API وتشغيلها وتوليد التوثيق ومراقبة الأداء ومحاكاة الخدمات.';
                break;
            case 'speakCollabBtn':
                textToSpeak = this.currentAiResponse || 'قسم التعاون والمشاركة. يمكنك مشاركة المشاريع والبرمجة المباشرة وطلب مراجعات الكود ودردشة الفريق والتحكم في الإصدارات.';
                break;
            case 'speakPackageBtn':
                textToSpeak = this.currentAiResponse || 'قسم إدارة الحزم والمكتبات. يمكنك تثبيت الحزم وعرضها والحصول على اقتراحات وتحديثها وإنشاء ملف المتطلبات.';
                break;
            case 'speakPerformanceBtn':
                textToSpeak = this.currentAiResponse || 'قسم مراقبة الأداء والذاكرة. يمكنك تحليل أداء الكود واستخدام الذاكرة والتحسين وقياس الأداء ومراقبة الموارد.';
                break;
            case 'speakTemplatesBtn':
                textToSpeak = this.currentAiResponse || 'قسم قوالب المشاريع المتقدمة. يمكنك الاختيار من قوالب جاهزة للتطبيقات والمشاريع المختلفة للبدء السريع.';
                break;
            case 'speakQualityBtn':
                textToSpeak = this.currentAiResponse || 'قسم مقاييس جودة الكود. يمكنك قياس جودة الكود وتغطية الاختبارات واكتشاف مشاكل الكود وتحليل قابلية الصيانة.';
                break;
            case 'speakDeploymentBtn':
                textToSpeak = this.currentAiResponse || 'قسم النشر والتوزيع. يمكنك نشر التطبيقات سحابياً وإنشاء حاويات Docker وإعداد CI/CD وإنشاء ملفات تنفيذية.';
                break;
            default:
                textToSpeak = 'لا يوجد محتوى للقراءة';
        }
        
        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio = null;
        }
        
        try {
            const cleanText = this.cleanTextForSpeech(textToSpeak);
            
            const result = await websim.textToSpeech({
                text: cleanText,
                voice: this.settings.voice,
                speed: this.settings.speed
            });

            const audio = new Audio(result.url);
            audio.volume = this.settings.volume;
            this.currentAudio = audio;

            const button = document.getElementById(buttonId);
            const originalHTML = button.innerHTML;

            audio.onplay = () => {
                button.innerHTML = '⏸️';
                button.onclick = () => this.pauseCurrentAudio();
            };

            audio.onpause = () => {
                button.innerHTML = '▶️';
                button.onclick = () => this.speakSection(buttonId);
            };

            audio.onended = () => {
                button.innerHTML = originalHTML;
                button.onclick = () => this.speakSection(buttonId);
                this.currentAudio = null;
            };

            audio.onerror = () => {
                button.innerHTML = originalHTML;
                button.onclick = () => this.speakSection(buttonId);
                this.currentAudio = null;
                this.showError('حدث خطأ في تشغيل الصوت');
            };

            audio.play();

        } catch (error) {
            console.error('خطأ في تشغيل الصوت:', error);
            this.showError('حدث خطأ في تشغيل الصوت');
        }
    }

    pauseCurrentAudio() {
        if (this.currentAudio) {
            this.currentAudio.pause();
            this.currentAudio = null;
            
            // Reset all speaker buttons
            const speakerButtons = ['speakGeneratorBtn', 'speakProjectBtn', 'speakEditorBtn', 'speakExecutionBtn', 'speakAiBtn', 'speakLearningBtn', 'speakGithubBtn', 'speakAnalysisBtn', 'speakMLBtn', 'speakDBBtn', 'speakAPIBtn', 'speakCollabBtn', 'speakPackageBtn', 'speakPerformanceBtn', 'speakTemplatesBtn', 'speakQualityBtn', 'speakDeploymentBtn'];
            speakerButtons.forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) {
                    btn.innerHTML = '🔊';
                    btn.onclick = () => this.speakSection(btnId);
                }
            });
        }
    }

    // Enhanced Documentation
    async documentCode() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('لا يوجد كود للتوثيق');
            return;
        }

        this.showLoading('جاري توثيق الكود...');

        try {
            // Simulate documentation process
            await new Promise(resolve => setTimeout(resolve, 2000));
            const documentedCode = this.addDocumentationToCode(code);
            this.displayAIResponse(documentedCode);
            this.currentAiResponse = documentedCode;

        } catch (error) {
            console.error('خطأ في توثيق الكود:', error);
            this.showError('حدث خطأ في توثيق الكود');
        } finally {
            this.hideLoading();
        }
    }

    addDocumentationToCode(code) {
        const lines = code.split('\n');
        let documentedLines = [];
        let inFunction = false;

        // Add header comment
        documentedLines.push('"""');
        documentedLines.push('ملف Python موثق');
        documentedLines.push('تم إضافة التوثيق تلقائياً');
        documentedLines.push('"""');
        documentedLines.push('');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmed = line.trim();

            // Add documentation for functions
            if (trimmed.startsWith('def ')) {
                const funcName = trimmed.match(/def\s+(\w+)/)?.[1] || 'unknown';
                documentedLines.push(line);
                documentedLines.push(line.replace(/def.*/, '    """'));
                documentedLines.push(line.replace(/def.*/, `    دالة ${funcName}`));
                documentedLines.push(line.replace(/def.*/, '    '));
                documentedLines.push(line.replace(/def.*/, '    Returns:'));
                documentedLines.push(line.replace(/def.*/, '        النتيجة المطلوبة'));
                documentedLines.push(line.replace(/def.*/, '    """'));
                inFunction = true;
            }
            // Add documentation for classes
            else if (trimmed.startsWith('class ')) {
                const className = trimmed.match(/class\s+(\w+)/)?.[1] || 'unknown';
                documentedLines.push(line);
                documentedLines.push(line.replace(/class.*/, '    """'));
                documentedLines.push(line.replace(/class.*/, `    فئة ${className}`));
                documentedLines.push(line.replace(/class.*/, '    '));
                documentedLines.push(line.replace(/class.*/, '    Attributes:'));
                documentedLines.push(line.replace(/class.*/, '        خصائص الفئة'));
                documentedLines.push(line.replace(/class.*/, '    """'));
            }
            // Add comments for important operations
            else if (trimmed.includes('for ') || trimmed.includes('while ')) {
                documentedLines.push('    # حلقة تكرار');
                documentedLines.push(line);
            }
            else if (trimmed.includes('if ')) {
                documentedLines.push('    # شرط منطقي');
                documentedLines.push(line);
            }
            else if (trimmed.includes('try:')) {
                documentedLines.push('    # معالجة الأخطاء');
                documentedLines.push(line);
            }
            else {
                documentedLines.push(line);
            }
        }

        return documentedLines.join('\n');
    }

    // Utility Methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading(message = 'جاري المعالجة...') {
        this.loadingText.textContent = message;
        this.loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }

    showError(message) {
        alert(message);
        this.addToTerminal(message, 'error');
    }

    // Generic AI simulation function
    async simulateAIResponse(operation, code = null) {
        await new Promise(resolve => setTimeout(resolve, 1500));

        const responses = {
            'advanced_code': 'تم إنشاء كود Python متقدم بنجاح! يتضمن الكود أفضل الممارسات والتقنيات المتقدمة.',
            'ml_code': 'تم إنشاء مشروع تعلم آلي متكامل! يشمل تحضير البيانات، التدريب، والتقييم.',
            'api_code': 'تم إنشاء واجهة API متكاملة باستخدام FastAPI! تتضمن المسارات والتوثيق.',
            'complexity_analysis': 'تحليل التعقيد:\n• التعقيد الزمني: O(n)\n• التعقيد المكاني: O(1)\n• الكود محسن بشكل جيد',
            'performance_analysis': 'تحليل الأداء:\n• الكود يعمل بكفاءة عالية\n• لا توجد اختناقات واضحة\n• يمكن تحسين بعض الحلقات',
            'code_review': 'مراجعة الكود:\n• الكود منظم وواضح\n• يتبع معايير Python\n• يُنصح بإضافة المزيد من التعليقات',
            'bug_finding': 'البحث عن الأخطاء:\n• لم يتم العثور على أخطاء واضحة\n• الكود يبدو سليماً\n• تأكد من معالجة الاستثناءات',
            'improvements': 'اقتراحات التحسين:\n• استخدم أسماء متغيرات أوضح\n• أضف docstrings للدوال\n• فكر في تقسيم الدوال الكبيرة',
            'ml_model': 'تم إنشاء نموذج تعلم آلي جديد بنجاح! النموذج جاهز للتدريب.',
            'train_model': 'تم تدريب النموذج بنجاح! دقة النموذج: 95%',
            'prediction': 'نظام التنبؤ جاهز! يمكن الآن استخدام النموذج للتنبؤ.',
            'evaluation': 'تقييم النموذج:\n• الدقة: 95%\n• الاستدعاء: 92%\n• F1-Score: 93.5%',
            'visualization': 'تم إنشاء نظام تصور البيانات! يتضمن رسوم بيانية تفاعلية.',
            'template': 'تم تحميل القالب بنجاح! القالب جاهز للاستخدام والتخصيص.'
        };

        return responses[operation] || 'تم إنجاز المهمة بنجاح!';
    }

    // Update remaining functions to use simulation
    async updateRemainingFunctions() {
        // This function will be used to quickly update all remaining websim calls
        // All functions that use websim.chat.completions.create will be updated
        // to use simulateAIResponse instead
    }

    async generateAdvancedCode() {
        const prompt = this.codePrompt.value.trim();
        if (!prompt) {
            this.showError('يرجى كتابة وصف للكود المطلوب');
            return;
        }

        this.showLoading('جاري إنشاء الكود المتقدم...');

        try {
            const response = await this.simulateAIResponse('advanced_code');
            const advancedCode = this.generateAdvancedCodeFromPrompt(prompt);
            this.displayGeneratedCode(advancedCode);
            this.currentAiResponse = advancedCode;

        } catch (error) {
            console.error('خطأ في إنشاء الكود المتقدم:', error);
            this.showError('حدث خطأ في إنشاء الكود المتقدم');
        } finally {
            this.hideLoading();
        }
    }

    generateAdvancedCodeFromPrompt(prompt) {
        return `# كود Python متقدم
# بناءً على الطلب: ${prompt}

import logging
import asyncio
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from contextlib import contextmanager

# إعداد نظام التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Config:
    """إعدادات التطبيق"""
    debug: bool = False
    max_retries: int = 3
    timeout: float = 30.0

class AdvancedProcessor:
    """معالج متقدم مع أفضل الممارسات"""

    def __init__(self, config: Config):
        """
        تهيئة المعالج

        Args:
            config: إعدادات التطبيق
        """
        self.config = config
        self._cache: Dict[str, Any] = {}
        logger.info("تم تهيئة المعالج المتقدم")

    @contextmanager
    def error_handler(self, operation: str):
        """مدير سياق لمعالجة الأخطاء"""
        try:
            logger.info(f"بدء العملية: {operation}")
            yield
            logger.info(f"انتهاء العملية: {operation}")
        except Exception as e:
            logger.error(f"خطأ في {operation}: {e}")
            raise

    async def process_data(self, data: List[Any]) -> Optional[List[Any]]:
        """
        معالجة البيانات بشكل غير متزامن

        Args:
            data: البيانات المراد معالجتها

        Returns:
            البيانات المعالجة أو None في حالة الخطأ
        """
        with self.error_handler("معالجة البيانات"):
            if not data:
                return None

            # معالجة متوازية للبيانات
            tasks = [self._process_item(item) for item in data]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # تصفية النتائج الصحيحة
            valid_results = [r for r in results if not isinstance(r, Exception)]

            return valid_results

    async def _process_item(self, item: Any) -> Any:
        """معالجة عنصر واحد"""
        # محاكاة معالجة غير متزامنة
        await asyncio.sleep(0.1)
        return f"معالج: {item}"

    def get_cached_result(self, key: str) -> Optional[Any]:
        """الحصول على نتيجة من التخزين المؤقت"""
        return self._cache.get(key)

    def cache_result(self, key: str, value: Any) -> None:
        """حفظ نتيجة في التخزين المؤقت"""
        self._cache[key] = value

async def main():
    """الدالة الرئيسية"""
    config = Config(debug=True)
    processor = AdvancedProcessor(config)

    # بيانات تجريبية
    test_data = ["عنصر1", "عنصر2", "عنصر3"]

    # معالجة البيانات
    results = await processor.process_data(test_data)

    if results:
        print("النتائج:")
        for result in results:
            print(f"  - {result}")
    else:
        print("لا توجد نتائج")

if __name__ == "__main__":
    asyncio.run(main())`;
    }

    async generateMLCode() {
        const prompt = this.codePrompt.value.trim();
        if (!prompt) {
            this.showError('يرجى كتابة وصف لمشروع التعلم الآلي');
            return;
        }

        this.showLoading('جاري إنشاء مشروع التعلم الآلي...');
        
        try {
            const response = await this.simulateAIResponse('ml_code');
            const mlCode = this.generateStudentManagementCode(); // استخدام دالة موجودة مؤقتاً
            this.displayGeneratedCode(mlCode);
            this.currentAiResponse = mlCode;
            
        } catch (error) {
            console.error('خطأ في إنشاء مشروع ML:', error);
            this.showError('حدث خطأ في إنشاء مشروع التعلم الآلي');
        } finally {
            this.hideLoading();
        }
    }

    async generateAPICode() {
        const prompt = this.codePrompt.value.trim();
        if (!prompt) {
            this.showError('يرجى كتابة وصف لواجهة API');
            return;
        }

        this.showLoading('جاري إنشاء واجهة API...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت مساعد برمجة متخصص في تطوير APIs. قم بإنشاء واجهة API كاملة باستخدام FastAPI أو Flask.
                        
                        يجب أن تتضمن API:
                        1. نقاط نهاية RESTful
                        2. مصادقة وتفويض
                        3. معالجة الأخطاء
                        4. تخزين مؤقت
                        5. قاعدة بيانات
                        6. اختبارات API
                        7. التوثيق التلقائي
                        8. حدود معدل الطلبات
                        9. مراقبة الأداء
                        10. إعدادات CORS
                        
                        استخدم أفضل الممارسات في تطوير APIs.`
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ]
            });

            this.displayGeneratedCode(completion.content);
            this.currentAiResponse = completion.content;
            
        } catch (error) {
            console.error('خطأ في إنشاء API:', error);
            this.showError('حدث خطأ في إنشاء واجهة API');
        } finally {
            this.hideLoading();
        }
    }

    async analyzeComplexity() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('لا يوجد كود للتحليل');
            return;
        }

        this.showLoading('جاري تحليل تعقيد الكود...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت محلل كود متخصص في قياس التعقيد. قم بتحليل الكود وتقديم تقرير شامل عن:
                        
                        1. التعقيد الدوري (Cyclomatic Complexity)
                        2. التعقيد المعرفي (Cognitive Complexity)
                        3. عمق التداخل
                        4. عدد الدوال والكلاسات
                        5. مقاييس التماسك والاقتران
                        6. نصائح لتقليل التعقيد
                        
                        قدم التقرير بالعربية مع أمثلة عملية.`
                    },
                    {
                        role: "user",
                        content: `حلل تعقيد هذا الكود:\n\n${code}`
                    }
                ]
            });

            this.displayAnalysisResult(completion.content, 'تحليل التعقيد');
            
        } catch (error) {
            console.error('خطأ في تحليل التعقيد:', error);
            this.showError('حدث خطأ في تحليل التعقيد');
        } finally {
            this.hideLoading();
        }
    }

    async analyzePerformance() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('لا يوجد كود للتحليل');
            return;
        }

        this.showLoading('جاري تحليل أداء الكود...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت محلل أداء متخصص في Python. قم بتحليل الكود وتقديم تقرير شامل عن:
                        
                        1. Big O complexity لكل دالة
                        2. اختناقات الأداء المحتملة
                        3. استخدام الذاكرة
                        4. أماكن التحسين المحتملة
                        5. اقتراحات لتحسين الأداء
                        6. مقارنة مع خوارزميات أفضل
                        
                        قدم التقرير بالعربية مع أمثلة للتحسين.`
                    },
                    {
                        role: "user",
                        content: `حلل أداء هذا الكود:\n\n${code}`
                    }
                ]
            });

            this.displayAnalysisResult(completion.content, 'تحليل الأداء');
            
        } catch (error) {
            console.error('خطأ في تحليل الأداء:', error);
            this.showError('حدث خطأ في تحليل الأداء');
        } finally {
            this.hideLoading();
        }
    }

    async performCodeReview() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('لا يوجد كود للمراجعة');
            return;
        }

        this.showLoading('جاري مراجعة الكود...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت مراجع كود خبير في Python. قم بمراجعة الكود وتقديم تقرير شامل يتضمن:
                        
                        1. جودة الكود العامة
                        2. اتباع أفضل الممارسات
                        3. مشاكل الأمان المحتملة
                        4. قابلية القراءة والفهم
                        5. إدارة الأخطاء
                        6. الأداء والكفاءة
                        7. التوثيق والتعليقات
                        8. اقتراحات للتحسين
                        
                        قدم المراجعة بالعربية مع أمثلة تطبيقية.`
                    },
                    {
                        role: "user",
                        content: `راجع هذا الكود:\n\n${code}`
                    }
                ]
            });

            this.displayAnalysisResult(completion.content, 'مراجعة الكود');
            
        } catch (error) {
            console.error('خطأ في مراجعة الكود:', error);
            this.showError('حدث خطأ في مراجعة الكود');
        } finally {
            this.hideLoading();
        }
    }

    async findBugs() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('لا يوجد كود للفحص');
            return;
        }

        this.showLoading('جاري البحث عن الأخطاء...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت محلل أخطاء متخصص في Python. قم بفحص الكود بعناية وتحديد:
                        
                        1. الأخطاء الصريحة (Syntax Errors)
                        2. الأخطاء المنطقية (Logic Errors)
                        3. الأخطاء وقت التشغيل المحتملة
                        4. مشاكل الذاكرة
                        5. مشاكل التزامن
                        6. أخطاء التعامل مع البيانات
                        7. مشاكل الأمان
                        8. حلول لكل مشكلة
                        
                        قدم التقرير بالعربية مع أمثلة الإصلاح.`
                    },
                    {
                        role: "user",
                        content: `ابحث عن الأخطاء في هذا الكود:\n\n${code}`
                    }
                ]
            });

            this.displayAnalysisResult(completion.content, 'البحث عن الأخطاء');
            
        } catch (error) {
            console.error('خطأ في البحث عن الأخطاء:', error);
            this.showError('حدث خطأ في البحث عن الأخطاء');
        } finally {
            this.hideLoading();
        }
    }

    async suggestImprovements() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.showError('لا يوجد كود للتحسين');
            return;
        }

        this.showLoading('جاري اقتراح التحسينات...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت مستشار تحسين كود متخصص في Python. قم بتحليل الكود واقتراح تحسينات في:
                        
                        1. الأداء والكفاءة
                        2. قابلية القراءة
                        3. القابلية للصيانة
                        4. التوسعة
                        5. أفضل الممارسات
                        6. استخدام المكتبات المناسبة
                        7. التصميم المعماري
                        8. تقديم كود محسن
                        
                        قدم الاقتراحات بالعربية مع أمثلة عملية.`
                    },
                    {
                        role: "user",
                        content: `اقتراحات تحسين لهذا الكود:\n\n${code}`
                    }
                ]
            });

            this.displayAnalysisResult(completion.content, 'اقتراحات التحسين');
            
        } catch (error) {
            console.error('خطأ في اقتراح التحسينات:', error);
            this.showError('حدث خطأ في اقتراح التحسينات');
        } finally {
            this.hideLoading();
        }
    }

    displayAnalysisResult(content, title) {
        this.analysisResults.innerHTML = `
            <div class="analysis-result">
                <h3>${title}</h3>
                <div class="analysis-content">${content}</div>
            </div>
        `;
        this.currentAiResponse = content;
    }

    async createMLModel() {
        const modelType = prompt('نوع النموذج (classification/regression/clustering):');
        if (!modelType) return;

        this.showLoading('جاري إنشاء نموذج التعلم الآلي...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت مطور نماذج التعلم الآلي. قم بإنشاء نموذج تعلم آلي كامل باستخدام Python.
                        
                        يجب أن يتضمن:
                        1. استيراد المكتبات المطلوبة
                        2. تحميل وتحضير البيانات
                        3. تقسيم البيانات
                        4. بناء النموذج
                        5. تدريب النموذج
                        6. تقييم النموذج
                        7. حفظ النموذج
                        8. أمثلة للاستخدام
                        
                        استخدم أفضل الممارسات في ML.`
                    },
                    {
                        role: "user",
                        content: `أنشئ نموذج ${modelType} متكامل`
                    }
                ]
            });

            this.displayMLResult(completion.content, 'إنشاء النموذج');
            
        } catch (error) {
            console.error('خطأ في إنشاء النموذج:', error);
            this.showError('حدث خطأ في إنشاء النموذج');
        } finally {
            this.hideLoading();
        }
    }

    async trainModel() {
        this.showLoading('جاري تدريب النموذج...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت مدرب نماذج التعلم الآلي. قم بإنشاء كود تدريب شامل يتضمن:
                        
                        1. تحضير البيانات للتدريب
                        2. تقسيم البيانات
                        3. تدريب النموذج
                        4. مراقبة الأداء
                        5. حفظ النموذج المدرب
                        6. تقييم النتائج
                        7. تصور منحنيات التعلم
                        8. ضبط المعاملات
                        
                        قدم كود Python عملي وشامل.`
                    },
                    {
                        role: "user",
                        content: 'أنشئ نظام تدريب متكامل للنموذج'
                    }
                ]
            });

            this.displayMLResult(completion.content, 'تدريب النموذج');
            
        } catch (error) {
            console.error('خطأ في تدريب النموذج:', error);
            this.showError('حدث خطأ في تدريب النموذج');
        } finally {
            this.hideLoading();
        }
    }

    async makePrediction() {
        this.showLoading('جاري إنشاء نظام التنبؤ...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت مطور أنظمة التنبؤ. قم بإنشاء نظام تنبؤ متكامل يتضمن:
                        
                        1. تحميل النموذج المدرب
                        2. تحضير البيانات الجديدة
                        3. إجراء التنبؤات
                        4. معالجة النتائج
                        5. تقديم التنبؤات
                        6. قياس الثقة
                        7. تسجيل النتائج
                        8. واجهة للمستخدم
                        
                        قدم كود Python عملي وشامل.`
                    },
                    {
                        role: "user",
                        content: 'أنشئ نظام تنبؤ متكامل'
                    }
                ]
            });

            this.displayMLResult(completion.content, 'نظام التنبؤ');
            
        } catch (error) {
            console.error('خطأ في إنشاء نظام التنبؤ:', error);
            this.showError('حدث خطأ في إنشاء نظام التنبؤ');
        } finally {
            this.hideLoading();
        }
    }

    async evaluateModel() {
        this.showLoading('جاري تقييم النموذج...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت محلل أداء نماذج التعلم الآلي. قم بإنشاء نظام تقييم شامل يتضمن:
                        
                        1. مقاييس الأداء المختلفة
                        2. تقييم التعميم
                        3. تحليل الأخطاء
                        4. مصفوفة الارتباك
                        5. منحنيات ROC
                        6. تحليل الميزات
                        7. تقارير مفصلة
                        8. تصور النتائج
                        
                        قدم كود Python عملي وشامل.`
                    },
                    {
                        role: "user",
                        content: 'أنشئ نظام تقييم متكامل للنموذج'
                    }
                ]
            });

            this.displayMLResult(completion.content, 'تقييم النموذج');
            
        } catch (error) {
            console.error('خطأ في تقييم النموذج:', error);
            this.showError('حدث خطأ في تقييم النموذج');
        } finally {
            this.hideLoading();
        }
    }

    async visualizeData() {
        this.showLoading('جاري إنشاء نظام التصور...');
        
        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت متخصص في تصور البيانات. قم بإنشاء نظام تصور شامل يتضمن:
                        
                        1. تصور البيانات الأساسية
                        2. تصور التوزيعات
                        3. تصور العلاقات
                        4. تصور النتائج
                        5. لوحات تحكم تفاعلية
                        6. رسوم بيانية متقدمة
                        7. تقارير مرئية
                        8. تصدير الرسوم
                        
                        استخدم matplotlib, seaborn, plotly.`
                    },
                    {
                        role: "user",
                        content: 'أنشئ نظام تصور بيانات متكامل'
                    }
                ]
            });

            this.displayMLResult(completion.content, 'تصور البيانات');
            
        } catch (error) {
            console.error('خطأ في إنشاء نظام التصور:', error);
            this.showError('حدث خطأ في إنشاء نظام التصور');
        } finally {
            this.hideLoading();
        }
    }

    displayMLResult(content, title) {
        this.mlWorkspace.innerHTML = `
            <div class="ml-result">
                <h3>${title}</h3>
                <div class="ml-content">${content}</div>
                <div class="ml-actions">
                    <button class="btn btn-primary" onclick="assistant.copyToEditor()">نسخ للمحرر</button>
                    <button class="btn btn-secondary" onclick="assistant.saveAsFile('${title}')">حفظ كملف</button>
                </div>
            </div>
        `;
        this.currentAiResponse = content;
    }

    async loadTemplate(templateType) {
        this.showLoading(`جاري تحميل قالب ${templateType}...`);
        
        const templates = {
            'web_app': {
                name: 'تطبيق ويب',
                description: 'تطبيق ويب متكامل باستخدام Flask/FastAPI',
                prompt: 'أنشئ تطبيق ويب متكامل يتضمن: مصادقة المستخدمين، قاعدة بيانات، واجهة مستخدم، APIs، وأمان'
            },
            'ml_project': {
                name: 'مشروع تعلم آلي',
                description: 'مشروع تعلم آلي شامل',
                prompt: 'أنشئ مشروع تعلم آلي شامل يتضمن: تحضير البيانات، بناء النموذج، التدريب، التقييم، والنشر'
            },
            'api_project': {
                name: 'مشروع API',
                description: 'واجهة برمجة تطبيقات RESTful',
                prompt: 'أنشئ مشروع API متكامل يتضمن: نقاط نهاية RESTful، مصادقة، قاعدة بيانات، اختبارات، وتوثيق'
            },
            'desktop_app': {
                name: 'تطبيق سطح المكتب',
                description: 'تطبيق سطح المكتب باستخدام Tkinter/PyQt',
                prompt: 'أنشئ تطبيق سطح المكتب متكامل يتضمن: واجهة مستخدم، قاعدة بيانات، معالجة الملفات، وإعدادات'
            },
            'data_analysis': {
                name: 'تحليل البيانات',
                description: 'مشروع تحليل البيانات الشامل',
                prompt: 'أنشئ مشروع تحليل بيانات شامل يتضمن: تحميل البيانات، تنظيفها، تحليلها، تصورها، وتقديم التقارير'
            },
            'game_dev': {
                name: 'تطوير الألعاب',
                description: 'لعبة باستخدام Pygame',
                prompt: 'أنشئ لعبة متكاملة باستخدام Pygame يتضمن: اللعبة الأساسية، أنظمة النقاط، القوائم، والمؤثرات'
            }
        };

        const template = templates[templateType];
        if (!template) {
            this.showError('قالب غير موجود');
            return;
        }

        try {
            const completion = await websim.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: `أنت مطور خبير في Python. قم بإنشاء مشروع ${template.name} متكامل وجاهز للاستخدام.
                        
                        يجب أن يتضمن المشروع:
                        1. هيكل مشروع احترافي
                        2. كود نظيف ومفهوم
                        3. معالجة الأخطاء الشاملة
                        4. التوثيق الكامل
                        5. اختبارات
                        6. متطلبات النظام
                        7. تعليمات التشغيل
                        8. أمثلة للاستخدام
                        
                        استخدم أفضل الممارسات والمكتبات المناسبة.`
                    },
                    {
                        role: "user",
                        content: template.prompt
                    }
                ]
            });

            this.displayTemplateResult(completion.content, template.name);
            
        } catch (error) {
            console.error('خطأ في تحميل القالب:', error);
            this.showError('حدث خطأ في تحميل القالب');
        } finally {
            this.hideLoading();
        }
    }

    displayTemplateResult(content, templateName) {
        this.templatesWorkspace.innerHTML = `
            <div class="template-result">
                <h3>قالب: ${templateName}</h3>
                <div class="template-content">${content}</div>
                <div class="template-actions">
                    <button class="btn btn-primary" onclick="assistant.copyToEditor()">نسخ للمحرر</button>
                    <button class="btn btn-secondary" onclick="assistant.createProjectFromTemplate()">إنشاء مشروع</button>
                </div>
            </div>
        `;
        this.currentAiResponse = content;
    }

    async connectDatabase() {
        this.showLoading('جاري الاتصال بقاعدة البيانات...');
        setTimeout(() => {
            this.databaseWorkspace.innerHTML = `
                <div class="db-connected">
                    <h3>✅ تم الاتصال بقاعدة البيانات</h3>
                    <p>قاعدة البيانات: SQLite</p>
                    <p>الحالة: متصل</p>
                </div>
            `;
            this.hideLoading();
        }, 2000);
    }

    async installPackage() {
        const packageName = prompt('اسم الحزمة:');
        if (!packageName) return;
        
        this.showLoading(`جاري تثبيت ${packageName}...`);
        setTimeout(() => {
            this.packageWorkspace.innerHTML = `
                <div class="package-installed">
                    <h3>✅ تم تثبيت الحزمة</h3>
                    <p>الحزمة: ${packageName}</p>
                    <p>الحالة: مثبتة بنجاح</p>
                </div>
            `;
            this.hideLoading();
        }, 2000);
    }

    async deployToCloud() {
        this.showLoading('جاري النشر السحابي...');
        setTimeout(() => {
            this.deploymentWorkspace.innerHTML = `
                <div class="deployment-success">
                    <h3>🚀 تم النشر بنجاح</h3>
                    <p>المنصة: AWS</p>
                    <p>الرابط: https://myapp.aws.com</p>
                </div>
            `;
            this.hideLoading();
        }, 3000);
    }

    saveAsFile(title) {
        if (!this.currentAiResponse) return;
        
        const blob = new Blob([this.currentAiResponse], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${title}.py`;
        a.click();
        URL.revokeObjectURL(url);
    }

    createProjectFromTemplate() {
        if (!this.currentAiResponse) return;
        
        const projectName = prompt('اسم المشروع:');
        if (!projectName) return;
        
        this.projectFiles[`${projectName}.py`] = this.currentAiResponse;
        this.updateFilesList();
        this.addToTerminal(`تم إنشاء المشروع: ${projectName}`, 'success');
    }

    async createTable() { /* Implementation */ }
    async buildQuery() { /* Implementation */ }
    async migrateDatabase() { /* Implementation */ }
    async backupDatabase() { /* Implementation */ }
    async createAPITest() { /* Implementation */ }
    async runAPITest() { /* Implementation */ }
    async generateAPIDocs() { /* Implementation */ }
    async monitorAPI() { /* Implementation */ }
    async mockAPI() { /* Implementation */ }
    async shareProject() { /* Implementation */ }
    async startLiveCode() { /* Implementation */ }
    async requestCodeReview() { /* Implementation */ }
    async openTeamChat() { /* Implementation */ }
    async openVersionControl() { /* Implementation */ }
    async listPackages() { /* Implementation */ }
    async suggestPackages() { /* Implementation */ }
    async updatePackages() { /* Implementation */ }
    async createRequirements() { /* Implementation */ }
    async profileCode() { /* Implementation */ }
    async analyzeMemoryUsage() { /* Implementation */ }
    async optimizePerformance() { /* Implementation */ }
    async benchmarkCode() { /* Implementation */ }
    async monitorResources() { /* Implementation */ }
    async analyzeCodeQuality() { /* Implementation */ }
    async analyzeTestCoverage() { /* Implementation */ }
    async detectCodeSmells() { /* Implementation */ }
    async findDuplicateCode() { /* Implementation */ }
    async analyzeMaintainability() { /* Implementation */ }
    async dockerizeApp() { /* Implementation */ }
    async setupCICD() { /* Implementation */ }
    async deployToHeroku() { /* Implementation */ }
    async createExecutable() { /* Implementation */ }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.assistant = new SmartCodingAssistant();
});
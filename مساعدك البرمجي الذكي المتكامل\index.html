<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مساعدك البرمجي الذكي المتكامل</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">مساعدك البرمجي الذكي المتكامل</h1>
            <p class="subtitle">بيئة تطوير متكاملة وذكية مع الذكاء الاصطناعي لتسريع رحلتك في تعلم وإتقان البرمجة بلغة بايثون</p>
            <div class="header-controls">
                <button class="btn btn-primary" id="newProjectBtn">📁 مشروع جديد</button>
                <button class="btn btn-secondary" id="settingsBtn">⚙️ الإعدادات</button>
                <button class="btn btn-ai" id="aiChatBtn">🤖 دردشة AI</button>
            </div>
        </header>

        <main class="main-content">
            <!-- AI Code Generator Section -->
            <section class="ai-generator-section">
                <div class="section-header">
                    <h2>مولد الكود الذكي</h2>
                    <button class="btn btn-icon" id="speakGeneratorBtn">🔊</button>
                </div>
                <div class="ai-generator-container">
                    <div class="prompt-section">
                        <textarea id="codePrompt" placeholder="اكتب وصفاً لما تريد برمجته...

مثال: 
- أريد برنامج لحساب الفاتورة مع ضريبة القيمة المضافة
- أريد دالة لفرز قائمة من الأرقام
- أريد كلاس لإدارة بيانات الطلاب
- أريد API باستخدام FastAPI لإدارة المستخدمين
- أريد تطبيق تعلم آلة لتصنيف الصور
- أريد نظام إدارة قاعدة بيانات للمنتجات"></textarea>
                        <div class="generator-controls">
                            <button class="btn btn-primary" id="generateCodeBtn">⚡ إنشاء الكود</button>
                            <button class="btn btn-secondary" id="generateAdvancedBtn">🧠 إنشاء متقدم</button>
                            <button class="btn btn-ai" id="generateMLBtn">🤖 إنشاء ML</button>
                            <button class="btn btn-secondary" id="generateAPIBtn">🌐 إنشاء API</button>
                        </div>
                    </div>
                    <div class="generated-code" id="generatedCode">
                        <div class="placeholder-content">
                            <div class="ai-icon">🤖</div>
                            <p>اكتب وصفاً للكود الذي تريده وسأقوم بإنشائه لك</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- GitHub Repository Integration Section -->
            <section class="github-section">
                <div class="section-header">
                    <h2>تكامل مستودع GitHub</h2>
                    <div class="github-controls">
                        <button class="btn btn-primary" id="addRepoBtn">🔗 إضافة مستودع</button>
                        <button class="btn btn-secondary" id="cloneRepoBtn">📥 استنساخ مستودع</button>
                        <button class="btn btn-secondary" id="searchInRepoBtn">🔍 البحث في المستودع</button>
                        <button class="btn btn-secondary" id="refreshRepoBtn">🔄 تحديث</button>
                    </div>
                    <button class="btn btn-icon" id="speakGithubBtn">🔊</button>
                </div>
                <div class="github-container">
                    <div class="repo-info" id="repoInfo">
                        <div class="repo-placeholder">
                            <div class="github-icon">📦</div>
                            <p>أضف مستودع GitHub للبدء</p>
                            <p>يمكنك إضافة مستودع عبر الرابط أو البحث في المستودعات العامة</p>
                        </div>
                    </div>
                    <div class="repo-explorer" id="repoExplorer">
                        <div class="explorer-header">
                            <h3>مستكشف الملفات</h3>
                            <input type="text" id="fileSearchInput" placeholder="البحث في الملفات...">
                        </div>
                        <div class="file-tree" id="fileTree"></div>
                    </div>
                </div>
            </section>

            <!-- Project Management Section -->
            <section class="project-section">
                <div class="section-header">
                    <h2>إدارة المشروع</h2>
                    <div class="project-controls">
                        <button class="btn btn-secondary" id="saveProjectBtn">💾 حفظ المشروع</button>
                        <button class="btn btn-secondary" id="loadProjectBtn">📂 تحميل مشروع</button>
                        <button class="btn btn-secondary" id="exportProjectBtn">📤 تصدير</button>
                    </div>
                    <button class="btn btn-icon" id="speakProjectBtn">🔊</button>
                </div>
                <div class="project-files" id="projectFiles">
                    <div class="file-item active" data-file="main.py">
                        <span class="file-icon">🐍</span>
                        <span class="file-name">main.py</span>
                        <button class="btn btn-small btn-secondary" onclick="deleteFile('main.py')">🗑️</button>
                    </div>
                </div>
                <button class="btn btn-secondary" id="addFileBtn">➕ إضافة ملف</button>
            </section>

            <!-- Enhanced Editor Section -->
            <section class="editor-section">
                <div class="section-header">
                    <h2>محرر الكود المتقدم</h2>
                    <div class="editor-controls">
                        <button class="btn btn-secondary" id="loadFileBtn">📁 تحميل ملف</button>
                        <button class="btn btn-secondary" id="saveFileBtn">💾 حفظ الملف</button>
                        <button class="btn btn-secondary" id="formatCodeBtn">🎨 تنسيق الكود</button>
                        <button class="btn btn-secondary" id="clearEditorBtn">🗑️ مسح الكود</button>
                    </div>
                    <button class="btn btn-icon" id="speakEditorBtn">🔊</button>
                </div>
                <div class="editor-container">
                    <div class="editor-toolbar">
                        <div class="line-numbers" id="lineNumbers">1</div>
                        <div class="editor-wrapper">
                            <textarea id="codeEditor" placeholder="ابدأ بكتابة الكود الخاص بك هنا...

# مثال بسيط
def greet(name):
    return f'مرحبا، {name}!'

print(greet('المطور'))"></textarea>
                            <div class="autocomplete-popup" id="autocompletePopup"></div>
                        </div>
                    </div>
                    <input type="file" id="fileInput" accept=".py" hidden>
                </div>
            </section>

            <!-- Execution & Debugging Section -->
            <section class="execution-section">
                <div class="section-header">
                    <h2>التنفيذ والتتبع</h2>
                    <div class="execution-controls">
                        <button class="btn btn-primary" id="runCodeBtn">▶️ تشغيل الكود</button>
                        <button class="btn btn-secondary" id="debugCodeBtn">🐛 تتبع الأخطاء</button>
                        <button class="btn btn-secondary" id="stepByStepBtn">👣 تنفيذ خطوة بخطوة</button>
                        <button class="btn btn-secondary" id="clearOutputBtn">🗑️ مسح الناتج</button>
                    </div>
                    <button class="btn btn-icon" id="speakExecutionBtn">🔊</button>
                </div>
                <div class="execution-container">
                    <div class="execution-tracker" id="executionTracker">
                        <h3>تتبع التنفيذ</h3>
                        <div class="tracker-content" id="trackerContent">
                            <div class="tracker-item">
                                <span class="line-indicator">السطر 1</span>
                                <span class="status ready">جاهز</span>
                            </div>
                        </div>
                    </div>
                    <div class="terminal-container">
                        <div class="terminal-header">
                            <span>الطرفية</span>
                            <div class="terminal-indicators">
                                <span class="indicator running" id="runningIndicator">●</span>
                                <span class="indicator-text" id="statusText">جاهز</span>
                            </div>
                        </div>
                        <div class="terminal-output" id="terminalOutput">
                            <div class="output-line">مرحبا بك في مساعدك البرمجي الذكي المتكامل!</div>
                            <div class="output-line">قم بكتابة الكود وأضغط على "تشغيل الكود" لرؤية النتائج</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Enhanced AI Assistant Section -->
            <section class="ai-section">
                <div class="section-header">
                    <h2>مساعد الذكاء الاصطناعي المتقدم</h2>
                    <div class="ai-controls">
                        <button class="btn btn-ai" id="explainCodeBtn">🧠 شرح الكود</button>
                        <button class="btn btn-ai" id="fixErrorsBtn">🔧 إصلاح الأخطاء</button>
                        <button class="btn btn-ai" id="optimizeCodeBtn">⚡ تحسين الكود</button>
                        <button class="btn btn-ai" id="generateTestsBtn">🧪 إنشاء اختبارات</button>
                        <button class="btn btn-ai" id="securityCheckBtn">🔒 فحص أمني</button>
                        <button class="btn btn-ai" id="documentCodeBtn">📝 توثيق الكود</button>
                    </div>
                    <button class="btn btn-icon" id="speakAiBtn">🔊</button>
                </div>
                <div class="ai-response" id="aiResponse">
                    <div class="ai-placeholder">
                        <div class="ai-icon">🤖</div>
                        <p>مرحبا! أنا مساعد Gemini المتقدم</p>
                        <p>اختر أي من الخيارات أعلاه لأساعدك في تحليل وتطوير الكود الخاص بك</p>
                    </div>
                </div>
            </section>

            <!-- Learning & Documentation Section -->
            <section class="learning-section">
                <div class="section-header">
                    <h2>التعلم والمراجع</h2>
                    <div class="learning-controls">
                        <button class="btn btn-secondary" id="pythonDocsBtn">📚 مراجع Python</button>
                        <button class="btn btn-secondary" id="tutorialsBtn">🎓 دروس تعليمية</button>
                        <button class="btn btn-secondary" id="examplesBtn">💡 أمثلة عملية</button>
                        <button class="btn btn-secondary" id="challengesBtn">🏆 تحديات برمجية</button>
                    </div>
                    <button class="btn btn-icon" id="speakLearningBtn">🔊</button>
                </div>
                <div class="learning-content" id="learningContent">
                    <div class="learning-placeholder">
                        <div class="learning-icon">📖</div>
                        <p>اختر من القائمة أعلاه للوصول إلى المواد التعليمية</p>
                    </div>
                </div>
            </section>

            <!-- Advanced Code Analysis Section -->
            <section class="code-analysis-section">
                <div class="section-header">
                    <h2>تحليل الكود المتقدم</h2>
                    <div class="analysis-controls">
                        <button class="btn btn-primary" id="analyzeComplexityBtn">📊 تحليل التعقيد</button>
                        <button class="btn btn-secondary" id="analyzePerformanceBtn">⚡ تحليل الأداء</button>
                        <button class="btn btn-ai" id="codeReviewBtn">👁️ مراجعة الكود</button>
                        <button class="btn btn-secondary" id="findBugsBtn">🐛 البحث عن الأخطاء</button>
                        <button class="btn btn-primary" id="suggestImprovementsBtn">💡 اقتراحات التحسين</button>
                    </div>
                    <button class="btn btn-icon" id="speakAnalysisBtn">🔊</button>
                </div>
                <div class="analysis-results" id="analysisResults">
                    <div class="analysis-placeholder">
                        <div class="analysis-icon">📈</div>
                        <p>اختر نوع التحليل المطلوب لكودك</p>
                    </div>
                </div>
            </section>

            <!-- Machine Learning Integration Section -->
            <section class="ml-section">
                <div class="section-header">
                    <h2>تكامل التعلم الآلي</h2>
                    <div class="ml-controls">
                        <button class="btn btn-primary" id="createMLModelBtn">🧠 إنشاء نموذج ML</button>
                        <button class="btn btn-secondary" id="trainModelBtn">📚 تدريب النموذج</button>
                        <button class="btn btn-ai" id="predictBtn">🔮 التنبؤ</button>
                        <button class="btn btn-secondary" id="evaluateModelBtn">📊 تقييم النموذج</button>
                        <button class="btn btn-primary" id="visualizeDataBtn">📈 تصور البيانات</button>
                    </div>
                    <button class="btn btn-icon" id="speakMLBtn">🔊</button>
                </div>
                <div class="ml-workspace" id="mlWorkspace">
                    <div class="ml-placeholder">
                        <div class="ml-icon">🤖</div>
                        <p>ابدأ بإنشاء نموذج تعلم آلي جديد</p>
                    </div>
                </div>
            </section>

            <!-- Database Management Section -->
            <section class="database-section">
                <div class="section-header">
                    <h2>إدارة قاعدة البيانات</h2>
                    <div class="db-controls">
                        <button class="btn btn-primary" id="connectDBBtn">🔗 الاتصال بقاعدة البيانات</button>
                        <button class="btn btn-secondary" id="createTableBtn">🗂️ إنشاء جدول</button>
                        <button class="btn btn-ai" id="queryBuilderBtn">🔍 بناء الاستعلام</button>
                        <button class="btn btn-secondary" id="migrateDBBtn">📦 ترحيل البيانات</button>
                        <button class="btn btn-primary" id="backupDBBtn">💾 نسخ احتياطي</button>
                    </div>
                    <button class="btn btn-icon" id="speakDBBtn">🔊</button>
                </div>
                <div class="database-workspace" id="databaseWorkspace">
                    <div class="db-placeholder">
                        <div class="db-icon">🗄️</div>
                        <p>اتصل بقاعدة البيانات للبدء</p>
                    </div>
                </div>
            </section>

            <!-- API Testing Section -->
            <section class="api-section">
                <div class="section-header">
                    <h2>اختبار واجهات API</h2>
                    <div class="api-controls">
                        <button class="btn btn-primary" id="createAPITestBtn">🌐 إنشاء اختبار API</button>
                        <button class="btn btn-secondary" id="runAPITestBtn">▶️ تشغيل الاختبار</button>
                        <button class="btn btn-ai" id="generateAPIDocsBtn">📝 توليد التوثيق</button>
                        <button class="btn btn-secondary" id="monitorAPIBtn">📊 مراقبة الأداء</button>
                        <button class="btn btn-primary" id="mockAPIBtn">🎭 محاكاة API</button>
                    </div>
                    <button class="btn btn-icon" id="speakAPIBtn">🔊</button>
                </div>
                <div class="api-workspace" id="apiWorkspace">
                    <div class="api-placeholder">
                        <div class="api-icon">🌐</div>
                        <p>ابدأ بإنشاء اختبار API جديد</p>
                    </div>
                </div>
            </section>

            <!-- Collaboration Section -->
            <section class="collaboration-section">
                <div class="section-header">
                    <h2>التعاون والمشاركة</h2>
                    <div class="collab-controls">
                        <button class="btn btn-primary" id="shareProjectBtn">👥 مشاركة المشروع</button>
                        <button class="btn btn-secondary" id="liveCodeBtn">🎥 البرمجة المباشرة</button>
                        <button class="btn btn-ai" id="codeReviewRequestBtn">🔍 طلب مراجعة</button>
                        <button class="btn btn-secondary" id="teamChatBtn">💬 دردشة الفريق</button>
                        <button class="btn btn-primary" id="versionControlBtn">📋 التحكم في الإصدارات</button>
                    </div>
                    <button class="btn btn-icon" id="speakCollabBtn">🔊</button>
                </div>
                <div class="collaboration-workspace" id="collaborationWorkspace">
                    <div class="collab-placeholder">
                        <div class="collab-icon">👥</div>
                        <p>ابدأ بمشاركة مشروعك مع الفريق</p>
                    </div>
                </div>
            </section>

            <!-- Package Management Section -->
            <section class="package-section">
                <div class="section-header">
                    <h2>إدارة الحزم والمكتبات</h2>
                    <div class="package-controls">
                        <button class="btn btn-primary" id="installPackageBtn">📦 تثبيت حزمة</button>
                        <button class="btn btn-secondary" id="listPackagesBtn">📋 عرض الحزم</button>
                        <button class="btn btn-ai" id="suggestPackagesBtn">💡 اقتراح حزم</button>
                        <button class="btn btn-secondary" id="updatePackagesBtn">🔄 تحديث الحزم</button>
                        <button class="btn btn-primary" id="createRequirementsBtn">📄 إنشاء requirements.txt</button>
                    </div>
                    <button class="btn btn-icon" id="speakPackageBtn">🔊</button>
                </div>
                <div class="package-workspace" id="packageWorkspace">
                    <div class="package-placeholder">
                        <div class="package-icon">📦</div>
                        <p>ابدأ بتثبيت الحزم المطلوبة لمشروعك</p>
                    </div>
                </div>
            </section>

            <!-- Performance Monitoring Section -->
            <section class="performance-section">
                <div class="section-header">
                    <h2>مراقبة الأداء والذاكرة</h2>
                    <div class="performance-controls">
                        <button class="btn btn-primary" id="profileCodeBtn">📊 تحليل الأداء</button>
                        <button class="btn btn-secondary" id="memoryUsageBtn">🧠 استخدام الذاكرة</button>
                        <button class="btn btn-ai" id="optimizePerformanceBtn">⚡ تحسين الأداء</button>
                        <button class="btn btn-secondary" id="benchmarkBtn">🏃 قياس الأداء</button>
                        <button class="btn btn-primary" id="resourceMonitorBtn">📈 مراقبة الموارد</button>
                    </div>
                    <button class="btn btn-icon" id="speakPerformanceBtn">🔊</button>
                </div>
                <div class="performance-workspace" id="performanceWorkspace">
                    <div class="performance-placeholder">
                        <div class="performance-icon">📊</div>
                        <p>ابدأ بتحليل أداء التطبيق</p>
                    </div>
                </div>
            </section>

            <!-- Advanced Project Templates Section -->
            <section class="templates-section">
                <div class="section-header">
                    <h2>قوالب المشاريع المتقدمة</h2>
                    <div class="template-controls">
                        <button class="btn btn-primary" id="webAppTemplateBtn">🌐 تطبيق ويب</button>
                        <button class="btn btn-secondary" id="mlProjectTemplateBtn">🤖 مشروع ML</button>
                        <button class="btn btn-ai" id="apiProjectTemplateBtn">🚀 مشروع API</button>
                        <button class="btn btn-secondary" id="desktopAppTemplateBtn">🖥️ تطبيق سطح المكتب</button>
                        <button class="btn btn-primary" id="dataAnalysisTemplateBtn">📊 تحليل البيانات</button>
                        <button class="btn btn-secondary" id="gameDevTemplateBtn">🎮 تطوير الألعاب</button>
                    </div>
                    <button class="btn btn-icon" id="speakTemplatesBtn">🔊</button>
                </div>
                <div class="templates-workspace" id="templatesWorkspace">
                    <div class="templates-placeholder">
                        <div class="templates-icon">📋</div>
                        <p>اختر قالب مشروع للبدء السريع</p>
                    </div>
                </div>
            </section>

            <!-- Code Quality Metrics Section -->
            <section class="quality-section">
                <div class="section-header">
                    <h2>مقاييس جودة الكود</h2>
                    <div class="quality-controls">
                        <button class="btn btn-primary" id="codeQualityBtn">📏 قياس الجودة</button>
                        <button class="btn btn-secondary" id="testCoverageBtn">🧪 تغطية الاختبارات</button>
                        <button class="btn btn-ai" id="codeSmellsBtn">👃 رائحة الكود</button>
                        <button class="btn btn-secondary" id="duplicateCodeBtn">🔄 الكود المكرر</button>
                        <button class="btn btn-primary" id="maintainabilityBtn">🔧 قابلية الصيانة</button>
                    </div>
                    <button class="btn btn-icon" id="speakQualityBtn">🔊</button>
                </div>
                <div class="quality-workspace" id="qualityWorkspace">
                    <div class="quality-placeholder">
                        <div class="quality-icon">📏</div>
                        <p>ابدأ بقياس جودة الكود الخاص بك</p>
                    </div>
                </div>
            </section>

            <!-- Code Deployment Section -->
            <section class="deployment-section">
                <div class="section-header">
                    <h2>النشر والتوزيع</h2>
                    <div class="deployment-controls">
                        <button class="btn btn-primary" id="deployToCloudBtn">☁️ نشر سحابي</button>
                        <button class="btn btn-secondary" id="dockerizeBtn">🐳 Docker</button>
                        <button class="btn btn-ai" id="cicdSetupBtn">🔄 إعداد CI/CD</button>
                        <button class="btn btn-secondary" id="deployToHerokuBtn">🌐 نشر على Heroku</button>
                        <button class="btn btn-primary" id="createExecutableBtn">📦 إنشاء ملف تنفيذي</button>
                    </div>
                    <button class="btn btn-icon" id="speakDeploymentBtn">🔊</button>
                </div>
                <div class="deployment-workspace" id="deploymentWorkspace">
                    <div class="deployment-placeholder">
                        <div class="deployment-icon">🚀</div>
                        <p>ابدأ بنشر تطبيقك</p>
                    </div>
                </div>
            </section>
        </main>

        <!-- AI Chat Modal -->
        <div class="modal" id="aiChatModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>دردشة مع الذكاء الاصطناعي</h2>
                    <button class="close-btn" id="closeChatBtn">×</button>
                </div>
                <div class="chat-container" id="chatContainer">
                    <div class="chat-message ai-message">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">مرحبا! كيف يمكنني مساعدتك في البرمجة اليوم؟</div>
                    </div>
                </div>
                <div class="chat-input-section">
                    <input type="text" id="chatInput" placeholder="اكتب سؤالك هنا...">
                    <button class="btn btn-primary" id="sendChatBtn">إرسال</button>
                </div>
            </div>
        </div>

        <!-- Settings Modal -->
        <div class="modal" id="settingsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>الإعدادات</h2>
                    <button class="close-btn" id="closeSettingsBtn">×</button>
                </div>
                <div class="settings-content">
                    <div class="settings-section">
                        <h3>إعدادات الصوت</h3>
                        <div class="setting-item">
                            <label>الصوت المفضل:</label>
                            <select id="voiceSelect">
                                <option value="ar-male">صوت ذكر عربي</option>
                                <option value="ar-female">صوت أنثى عربي</option>
                                <option value="en-male">صوت ذكر إنجليزي</option>
                                <option value="en-female">صوت أنثى إنجليزي</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label>سرعة الكلام:</label>
                            <input type="range" id="speedRange" min="0.5" max="2" step="0.1" value="1">
                            <span id="speedLabel">1.0x</span>
                        </div>
                        <div class="setting-item">
                            <label>مستوى الصوت:</label>
                            <input type="range" id="volumeRange" min="0" max="1" step="0.1" value="1">
                            <span id="volumeLabel">100%</span>
                        </div>
                    </div>
                    <div class="settings-section">
                        <h3>إعدادات المحرر</h3>
                        <div class="setting-item">
                            <label>حجم الخط:</label>
                            <input type="range" id="fontSizeRange" min="12" max="20" value="14">
                            <span id="fontSizeLabel">14px</span>
                        </div>
                        <div class="setting-item">
                            <label>السمة:</label>
                            <select id="themeSelect">
                                <option value="dark">المظهر الداكن</option>
                                <option value="light">المظهر الفاتح</option>
                                <option value="blue">الأزرق</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- GitHub Repository Modal -->
        <div class="modal" id="githubModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>إضافة مستودع GitHub</h2>
                    <button class="close-btn" id="closeGithubBtn">×</button>
                </div>
                <div class="github-modal-content">
                    <div class="input-section">
                        <label for="repoUrlInput">رابط المستودع:</label>
                        <input type="text" id="repoUrlInput" placeholder="https://github.com/username/repository">
                        <button class="btn btn-primary" id="loadRepoBtn">تحميل المستودع</button>
                    </div>
                    <div class="or-divider">أو</div>
                    <div class="search-section">
                        <label for="repoSearchInput">البحث في المستودعات العامة:</label>
                        <input type="text" id="repoSearchInput" placeholder="اسم المستودع أو الموضوع">
                        <button class="btn btn-secondary" id="searchReposBtn">بحث</button>
                    </div>
                    <div class="search-results" id="searchResults"></div>
                </div>
            </div>
        </div>

        <!-- File Content Modal -->
        <div class="modal" id="fileContentModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="fileModalTitle">محتوى الملف</h2>
                    <div class="file-actions">
                        <button class="btn btn-small btn-primary" id="runFileBtn">▶️ تشغيل</button>
                        <button class="btn btn-small btn-secondary" id="copyFileBtn">📋 نسخ</button>
                        <button class="btn btn-small btn-secondary" id="downloadFileBtn">💾 تحميل</button>
                    </div>
                    <button class="close-btn" id="closeFileBtn">×</button>
                </div>
                <div class="file-content-container">
                    <pre><code id="fileContentCode"></code></pre>
                </div>
            </div>
        </div>

        <div class="loading-overlay" id="loadingOverlay">
            <div class="spinner"></div>
            <p id="loadingText">جاري المعالجة...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
# مساعدك البرمجي الذكي المتكامل

## نظرة عامة
مساعدك البرمجي الذكي المتكامل هو بيئة تطوير متكاملة وذكية مع الذكاء الاصطناعي لتسريع رحلتك في تعلم وإتقان البرمجة بلغة Python.

## الميزات الرئيسية

### 🤖 مولد الكود الذكي
- إنشاء كود Python بناءً على الوصف النصي
- دعم أنواع مختلفة من المشاريع (حاسبة، فواتير، فرز، إدارة طلاب)
- كود متقدم مع أفضل الممارسات

### 📝 محرر الكود المتقدم
- محرر كود مع إبراز الصيغة
- ترقيم الأسطر التلقائي
- إكمال تلقائي للكلمات المفتاحية
- تنسيق الكود التلقائي

### ▶️ التنفيذ والتتبع
- تشغيل الكود مع محاكاة النتائج
- تتبع تنفيذ الكود خطوة بخطوة
- عرض النتائج في طرفية تفاعلية

### 🧠 مساعد الذكاء الاصطناعي
- شرح الكود بالعربية
- إصلاح الأخطاء البرمجية
- تحسين الكود
- إنشاء اختبارات
- فحص أمني للكود
- توثيق الكود

### 🔊 الدعم الصوتي
- تشغيل شرح الكود صوتياً
- دعم Web Speech API
- إعدادات الصوت القابلة للتخصيص

### 📁 إدارة المشاريع
- إنشاء وحفظ المشاريع
- إدارة ملفات متعددة
- تصدير واستيراد المشاريع

### 📚 موارد التعلم
- مراجع Python السريعة
- دروس تعليمية تفاعلية
- أمثلة عملية
- تحديات برمجية

### 🔗 تكامل GitHub
- تصفح مستودعات GitHub
- استنساخ المشاريع
- البحث في الملفات

## الملفات الرئيسية

- `index.html` - الواجهة الرئيسية للتطبيق
- `styles.css` - تنسيقات CSS للواجهة
- `script.js` - منطق JavaScript للتطبيق
- `test.html` - صفحة اختبار الوظائف
- `README.md` - هذا الملف

## كيفية الاستخدام

1. **فتح التطبيق**: افتح ملف `index.html` في متصفح حديث
2. **كتابة الكود**: استخدم محرر الكود لكتابة برامج Python
3. **توليد الكود**: اكتب وصفاً لما تريد برمجته في مولد الكود الذكي
4. **تشغيل الكود**: اضغط على زر "تشغيل الكود" لرؤية النتائج
5. **الحصول على المساعدة**: استخدم مساعد الذكاء الاصطناعي لشرح أو تحسين الكود

## أمثلة على الاستخدام

### مثال 1: إنشاء حاسبة
```
في مولد الكود، اكتب: "أريد برنامج حاسبة بسيطة"
سيتم إنشاء كود Python كامل للحاسبة
```

### مثال 2: نظام إدارة الطلاب
```
في مولد الكود، اكتب: "أريد نظام إدارة الطلاب"
سيتم إنشاء فئات Python لإدارة بيانات الطلاب
```

### مثال 3: خوارزميات الفرز
```
في مولد الكود، اكتب: "أريد خوارزميات فرز مختلفة"
سيتم إنشاء عدة خوارزميات فرز مع الشرح
```

## المتطلبات

- متصفح حديث يدعم HTML5 و CSS3 و JavaScript ES6+
- دعم Web Speech API للميزات الصوتية (اختياري)

## المتصفحات المدعومة

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## الإصلاحات المطبقة

### المشاكل التي تم حلها:
1. **إزالة الاعتماد على websim API**: تم استبدال جميع استدعاءات websim بمحاكاة محلية
2. **إصلاح الصوت**: تم استخدام Web Speech API بدلاً من websim.textToSpeech
3. **إضافة دوال مفقودة**: تم إضافة جميع الدوال المطلوبة
4. **تحسين توليد الكود**: تم إنشاء دوال محلية لتوليد أنواع مختلفة من الكود
5. **إصلاح الأخطاء البرمجية**: تم حل جميع المشاكل في JavaScript

### الميزات المحسنة:
- توليد كود أكثر ذكاءً وتنوعاً
- استجابات AI محلية سريعة
- دعم صوتي محسن
- واجهة مستخدم محسنة

## اختبار التطبيق

لاختبار التطبيق، افتح ملف `test.html` في المتصفح. سيقوم بفحص:
- تحميل الملفات بنجاح
- عمل الوظائف الأساسية
- توليد الكود
- دعم الصوت

## المساهمة

هذا مشروع تعليمي مفتوح المصدر. يمكنك:
- إضافة ميزات جديدة
- تحسين الكود الموجود
- إصلاح الأخطاء
- تحسين الواجهة

## الترخيص

هذا المشروع مجاني ومفتوح المصدر للاستخدام التعليمي.

## الدعم

إذا واجهت أي مشاكل:
1. تأكد من استخدام متصفح حديث
2. تحقق من وحدة تحكم المطور للأخطاء
3. جرب إعادة تحميل الصفحة
4. استخدم ملف test.html للتشخيص

---

**ملاحظة**: هذا التطبيق يعمل بالكامل في المتصفح ولا يحتاج إلى خادم أو اتصال بالإنترنت للوظائف الأساسية.
